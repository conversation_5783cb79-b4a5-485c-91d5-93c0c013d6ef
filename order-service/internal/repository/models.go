package repository

import (
	"time"
	"github.com/google/uuid"
)

// ID 类型定义 - 修改为 UUID 类型
type ID uuid.UUID

// NewID 创建新的 UUID ID
func NewID() ID {
	return ID(uuid.New())
}

// ParseID 从字符串解析 UUID ID
func ParseID(s string) (ID, error) {
	id, err := uuid.Parse(s)
	if err != nil {
		return ID{}, err
	}
	return ID(id), nil
}

// String 返回 ID 的字符串表示
func (id ID) String() string {
	return uuid.UUID(id).String()
}

// IsZero 检查 ID 是否为零值
func (id ID) IsZero() bool {
	return uuid.UUID(id) == uuid.Nil
}

// IDFromInt64 从 int64 转换为 ID（用于向后兼容）
// 注意：这是临时的兼容性函数，应该逐步迁移到直接使用 UUID
func IDFromInt64(i int64) ID {
	if i == 0 {
		return ID(uuid.Nil)
	}
	// 为了向后兼容，我们将 int64 转换为确定性的 UUID
	// 这里使用一个简单的方法：将 int64 转换为字符串，然后生成 UUID
	// 在实际应用中，您可能需要维护一个 int64 到 UUID 的映射表
	return NewID() // 临时返回新的 UUID，实际应用中需要映射表
}

// 状态类型定义
type OrderStatus string
type PaymentStatus string
type FuelTransactionStatus string
// LinkStatus 链接状态
type LinkStatus string

// 订单状态常量
const (
	OrderStatusNew        OrderStatus = "new"
	OrderStatusProcessing OrderStatus = "processing"
	OrderStatusCompleted  OrderStatus = "completed"
	OrderStatusCancelled  OrderStatus = "cancelled"
)

// 支付状态常量
const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusRefunded  PaymentStatus = "refunded"
)

// 燃油交易状态常量
const (
	FuelTransactionStatusPending   FuelTransactionStatus = "pending"
	FuelTransactionStatusProcessed FuelTransactionStatus = "processed"
	FuelTransactionStatusCancelled FuelTransactionStatus = "cancelled"
)

// 关联状态常量
const (
	LinkStatusActive   LinkStatus = "active"
	LinkStatusInactive LinkStatus = "inactive"
	LinkStatusReserved LinkStatus = "reserved" // 新增：预留状态，用于支付前的金额预留
)

// 通用分页参数
type Pagination struct {
	Page  int
	Limit int
}

// 通用排序参数
type SortOrder struct {
	Field     string
	Direction string // "asc" 或 "desc"
}

// 订单筛选参数
type OrderFilter struct {
	CustomerID    *ID
	StationID     *ID
	Status        *OrderStatus
	DateFrom      *time.Time
	DateTo        *time.Time
	OrderNumber   *string
	ProductType   *string
	PaymentMethod *string
}

// 燃油交易筛选参数
type FuelTransactionFilter struct {
	StationID         *ID
	Status            *FuelTransactionStatus
	PumpID            *string
	MemberID          *ID
	DateFrom          *time.Time
	DateTo            *time.Time
	TransactionNumber *string
	FuelType          *string
	FuelGrade         *string
}

// 链接筛选参数
type FuelTransactionOrderLinkFilter struct {
	FuelTransactionID *ID
	OrderID           *ID
	Status            *LinkStatus
	DateFrom          *time.Time
	DateTo            *time.Time
}

// Order 数据传输对象
type Order struct {
	ID             ID
	OrderNumber    string
	CustomerID     *int64   // 保持 int64 类型
	CustomerName   *string
	CustomerPhone  *string  // 新增：客户手机号
	VehicleType    *string  // 新增：车型
	LicensePlate   *string  // 新增：车牌号
	StationID      int64    // 保持 int64 类型
	Status         OrderStatus
	TotalAmount    float64
	DiscountAmount float64
	FinalAmount    float64
	TaxAmount      float64
	PaidAmount     float64
	Metadata       map[string]interface{}
	CreatedAt      time.Time
	UpdatedAt      time.Time
	CompletedAt    *time.Time
	CancelledAt    *time.Time
	Items          []OrderItem
	Promotions     []OrderPromotion
	Payments       []OrderPayment

	// 新增的关联字段（通过join查询获取）
	StationName       *string                `json:"station_name,omitempty"`       // 站点名称
	StaffName         *string                `json:"staff_name,omitempty"`         // 员工姓名
}

// OrderItem 数据传输对象
type OrderItem struct {
	ID             ID
	OrderID        ID
	ProductID      ID
	ProductName    string
	ProductType    string
	Quantity       float64
	UnitPrice      float64
	TotalPrice     float64
	DiscountAmount float64
	FinalPrice     float64
	TaxAmount      float64
	TaxRate        float64
	FuelGrade      *string
	PumpID         *string
	NozzleID       *string                `json:"nozzle_id,omitempty"`      // 新增：油枪ID
	Metadata       map[string]interface{}
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

// OrderPromotion 数据传输对象
type OrderPromotion struct {
	ID                 ID
	OrderID            ID
	PromotionID        string  // 改为string类型以支持UUID
	PromotionName      string
	PromotionType      string
	DiscountAmount     float64
	FreeItemID         *ID
	FreeItemName       *string
	FreeItemQuantity   *float64
	MinimumOrderAmount *float64
	Metadata           map[string]interface{}
	CreatedAt          time.Time
}

// OrderPayment 数据传输对象
type OrderPayment struct {
	ID               ID
	OrderID          ID
	PaymentMethod     string
	PaymentMethodName *string  // 支付方式名称（通过join查询获取）
	PaymentReference  *string
	Amount            float64
	Status           PaymentStatus
	TransactionID    *string
	Metadata         map[string]interface{}
	CreatedAt        time.Time
	UpdatedAt        time.Time
	CompletedAt      *time.Time
}

// FuelTransaction 数据传输对象
type FuelTransaction struct {
	ID                ID
	TransactionNumber string
	StationID         int64    // 保持 int64 类型
	PumpID            string
	NozzleID          string
	FuelType          string
	FuelGrade         string
	UnitPrice         float64
	Volume            float64
	Amount            float64
	Status            FuelTransactionStatus
	MemberCardID      *string
	MemberID          *int64  // 保持 int64 类型
	EmployeeID        *ID     // UUID 类型
	FCCTransactionID  *string
	POSTerminalID     *string
	StartTotalizer            *float64   // 加油开始时的累计计数器读数
	EndTotalizer              *float64   // 加油结束时的累计计数器读数
	NozzleStartTime           *time.Time // 油枪开始加油时间
	NozzleEndTime             *time.Time // 油枪挂枪时间
	TotalizerContinuityStatus *string    // 泵码连续性状态：normal/abnormal/unknown
	Metadata                  map[string]interface{}
	CreatedAt         time.Time
	UpdatedAt         time.Time
	ProcessedAt       *time.Time
	CancelledAt       *time.Time
}

// FuelTransactionOrderLink 数据传输对象
type FuelTransactionOrderLink struct {
	ID                ID
	FuelTransactionID ID
	OrderID           ID
	AllocatedAmount   float64
	Status            LinkStatus
	Metadata          map[string]interface{}
	CreatedAt         time.Time
	UpdatedAt         time.Time
	DeactivatedAt     *time.Time
}
