package repository

import (
	"context"
	"time"
)

// StationRepository 站点存储库接口
type StationRepository interface {
	// GetStationByID 根据ID获取站点信息
	GetStationByID(ctx context.Context, stationID int64) (*Station, error)

	// GetStationName 根据ID获取站点名称
	GetStationName(ctx context.Context, stationID int64) (string, error)

	// GetStationsBatch 批量获取站点信息
	GetStationsBatch(ctx context.Context, stationIDs []int64) (map[int64]*Station, error)
}

// Station 站点实体
type Station struct {
	ID                     int64  `json:"id" db:"id"`  // Station ID 是 int64 类型
	SiteCode               string `json:"site_code" db:"site_code"`
	SiteName               string `json:"site_name" db:"site_name"`
	Address                string `json:"address" db:"address"`
	Latitude               *float64 `json:"latitude" db:"latitude"`
	Longitude              *float64 `json:"longitude" db:"longitude"`
	AdministrativeDivision string `json:"administrative_division" db:"administrative_division"`
	BusinessHours          string `json:"business_hours" db:"business_hours"`
	BusinessStatus         string `json:"business_status" db:"business_status"`
	ContactInfo            string `json:"contact_info" db:"contact_info"`
	ManagerID              *ID    `json:"manager_id" db:"manager_id"`
	ManagementLevel        *string `json:"management_level" db:"management_level"`
	ParentOrganization     *string `json:"parent_organization" db:"parent_organization"`
	CreatedAt              time.Time `json:"created_at" db:"created_at"`
	UpdatedAt              time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy              *ID    `json:"created_by" db:"created_by"`
	UpdatedBy              *ID    `json:"updated_by" db:"updated_by"`
	Version                int    `json:"version" db:"version"`
} 