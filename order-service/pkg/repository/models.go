package repository

import (
	"encoding/json"
	"time"
	"github.com/google/uuid"
)

// ID 类型定义 - 修改为 UUID 类型
type ID uuid.UUID

// NewID 创建新的 UUID ID
func NewID() ID {
	return ID(uuid.New())
}

// ParseID 从字符串解析 UUID ID
func ParseID(s string) (ID, error) {
	id, err := uuid.Parse(s)
	if err != nil {
		return ID{}, err
	}
	return ID(id), nil
}

// String 返回 ID 的字符串表示
func (id ID) String() string {
	return uuid.UUID(id).String()
}

// IsZero 检查 ID 是否为零值
func (id ID) IsZero() bool {
	return uuid.UUID(id) == uuid.Nil
}

// MarshalJSON 实现 json.Marshaler 接口，将 UUID 序列化为字符串
func (id ID) MarshalJSON() ([]byte, error) {
	return json.Marshal(uuid.UUID(id).String())
}

// UnmarshalJSON 实现 json.Unmarshaler 接口，从字符串反序列化 UUID
func (id *ID) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	u, err := uuid.Parse(s)
	if err != nil {
		return err
	}

	*id = ID(u)
	return nil
}

// IDFromInt64 从 int64 转换为 ID（用于向后兼容）
// 注意：这是临时的兼容性函数，应该逐步迁移到直接使用 UUID
func IDFromInt64(i int64) ID {
	if i == 0 {
		return ID(uuid.Nil)
	}
	// 为了向后兼容，我们将 int64 转换为确定性的 UUID
	// 这里使用一个简单的方法：将 int64 转换为字符串，然后生成 UUID
	// 在实际应用中，您可能需要维护一个 int64 到 UUID 的映射表
	return NewID() // 临时返回新的 UUID，实际应用中需要映射表
}

// 状态类型定义
type OrderStatus string
type PaymentStatus string
type FuelTransactionStatus string
type LinkStatus string
type ShiftStatus string

// 订单状态常量
const (
	OrderStatusNew        OrderStatus = "new"
	OrderStatusProcessing OrderStatus = "processing"
	OrderStatusCompleted  OrderStatus = "completed"
	OrderStatusCancelled  OrderStatus = "cancelled"
)

// 支付状态常量
const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusRefunded  PaymentStatus = "refunded"
)

// 燃油交易状态常量
const (
	FuelTransactionStatusPending   FuelTransactionStatus = "pending"
	FuelTransactionStatusProcessed FuelTransactionStatus = "processed"
	FuelTransactionStatusCancelled FuelTransactionStatus = "cancelled"
)

// 关联状态常量
const (
	LinkStatusActive   LinkStatus = "active"
	LinkStatusInactive LinkStatus = "inactive"
	LinkStatusReserved LinkStatus = "reserved" // 新增：预留状态，用于支付前的金额预留
)

// 班次状态常量
const (
	ShiftStatusActive ShiftStatus = "active" // 表示班次正在进行中
	ShiftStatusClosed ShiftStatus = "closed" // 表示班次已结束
)

// 通用分页参数
type Pagination struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

// 通用排序参数
type SortOrder struct {
	Field     string `json:"field"`
	Direction string `json:"direction"` // "asc" 或 "desc"
}

// 订单筛选参数
type OrderFilter struct {
	CustomerID    *ID          `json:"customer_id,omitempty"`
	StationID     *ID          `json:"station_id,omitempty"`
	Status        *OrderStatus `json:"status,omitempty"`
	DateFrom      *time.Time   `json:"date_from,omitempty"`
	DateTo        *time.Time   `json:"date_to,omitempty"`
	OrderNumber   *string      `json:"order_number,omitempty"`
	ProductType   *string      `json:"product_type,omitempty"`
	PaymentMethod *string      `json:"payment_method,omitempty"`
	EmployeeNo    *string      `json:"employee_no,omitempty"`    // 兼容字段
	StaffCardID   *ID          `json:"staff_card_id,omitempty"`  // 新筛选条件
}

// 燃油交易筛选参数
type FuelTransactionFilter struct {
	StationID                   *ID                    `json:"station_id,omitempty"`
	Status                      *FuelTransactionStatus `json:"status,omitempty"`
	PumpID                      *string                `json:"pump_id,omitempty"`
	MemberID                    *ID                    `json:"member_id,omitempty"`
	EmployeeID                  *ID                    `json:"employee_id,omitempty"`   // 兼容字段
	StaffCardID                 *ID                    `json:"staff_card_id,omitempty"` // 新筛选条件
	ShiftID                     *ID                    `json:"shift_id,omitempty"`      // 班次筛选
	DateFrom                    *time.Time             `json:"date_from,omitempty"`
	DateTo                      *time.Time             `json:"date_to,omitempty"`
	TransactionNumber           *string                `json:"transaction_number,omitempty"`
	FuelType                    *string                `json:"fuel_type,omitempty"`
	FuelGrade                   *string                `json:"fuel_grade,omitempty"`
	FuelTypeCompat              *string                `json:"fuel_type_compat,omitempty"` // 兼容字段：同时搜索fuel_type和fuel_grade
	Tank                        *int                   `json:"tank,omitempty"`
	VehicleType                 *string                `json:"vehicle_type,omitempty"`                 // 车型筛选
	PumpNozzle                  *string                `json:"pump_nozzle,omitempty"`                  // 泵枪组合筛选
	TotalizerContinuityStatus   *string                `json:"totalizer_continuity_status,omitempty"`  // 泵码连续性状态筛选
}

// 链接筛选参数
type FuelTransactionOrderLinkFilter struct {
	FuelTransactionID *ID         `json:"fuel_transaction_id,omitempty"`
	OrderID           *ID         `json:"order_id,omitempty"`
	Status            *LinkStatus `json:"status,omitempty"`
	DateFrom          *time.Time  `json:"date_from,omitempty"`
	DateTo            *time.Time  `json:"date_to,omitempty"`
}

// 班次筛选参数
type ShiftFilter struct {
	StationID      *ID          `json:"station_id,omitempty"`
	Status         *ShiftStatus `json:"status,omitempty"`
	ShiftNumber    *string      `json:"shift_number,omitempty"`
	DateFrom       *time.Time   `json:"date_from,omitempty"`
	DateTo         *time.Time   `json:"date_to,omitempty"`
	IncludeDeleted *bool        `json:"include_deleted,omitempty"`
}

// Order 数据传输对象
type Order struct {
	ID             ID                     `json:"id"`
	OrderNumber    string                 `json:"order_number"`
	CustomerID     *int64                 `json:"customer_id,omitempty"`     // 保持 int64 类型
	CustomerName   *string                `json:"customer_name,omitempty"`
	CustomerPhone  *string                `json:"customer_phone,omitempty"`  // 客户手机号
	VehicleType    *string                `json:"vehicle_type,omitempty"`    // 车型
	LicensePlate   *string                `json:"license_plate,omitempty"`   // 车牌号
	StationID      int64                  `json:"station_id"`                // 保持 int64 类型
	EmployeeNo     *string                `json:"employee_no,omitempty"`     // 兼容字段，逐步废弃
	StaffCardID    *ID                    `json:"staff_card_id,omitempty"`   // 新字段，员工卡ID
	Status         OrderStatus            `json:"status"`
	TotalAmount    float64                `json:"total_amount"`
	DiscountAmount float64                `json:"discount_amount"`
	FinalAmount    float64                `json:"final_amount"`
	TaxAmount      float64                `json:"tax_amount"`
	PaidAmount     float64                `json:"paid_amount"`
	Metadata       map[string]interface{} `json:"metadata"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	CompletedAt    *time.Time             `json:"completed_at,omitempty"`
	CancelledAt    *time.Time             `json:"cancelled_at,omitempty"`
	Items          []OrderItem            `json:"items,omitempty"`
	Promotions     []OrderPromotion       `json:"promotions,omitempty"`
	Payments       []OrderPayment         `json:"payments,omitempty"`
	
	// 关联的员工卡信息（通过join查询获取）
	StaffCard         *StaffCard             `json:"staff_card,omitempty"`
	
	// 新增的关联字段（通过join查询获取）
	StationName       *string                `json:"station_name,omitempty"`       // 站点名称
	StaffName         *string                `json:"staff_name,omitempty"`         // 员工姓名
}

// OrderItem 数据传输对象
type OrderItem struct {
	ID             ID                     `json:"id"`
	OrderID        ID                     `json:"order_id"`
	ProductID      int64                  `json:"product_id"`                // 保持 int64 类型
	ProductName    string                 `json:"product_name"`
	ProductType    string                 `json:"product_type"`
	Quantity       float64                `json:"quantity"`
	UnitPrice      float64                `json:"unit_price"`
	TotalPrice     float64                `json:"total_price"`
	DiscountAmount float64                `json:"discount_amount"`
	FinalPrice     float64                `json:"final_price"`
	TaxAmount      float64                `json:"tax_amount"`
	TaxRate        float64                `json:"tax_rate"`
	FuelGrade      *string                `json:"fuel_grade,omitempty"`
	PumpID         *string                `json:"pump_id,omitempty"`
	NozzleID       *string                `json:"nozzle_id,omitempty"`      // 新增：油枪ID
	Metadata       map[string]interface{} `json:"metadata"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
}

// OrderPromotion 数据传输对象
type OrderPromotion struct {
	ID                 ID                     `json:"id"`
	OrderID            ID                     `json:"order_id"`
	PromotionID        string                 `json:"promotion_id"`  // 改为string类型以支持UUID
	PromotionName      string                 `json:"promotion_name"`
	PromotionType      string                 `json:"promotion_type"`
	DiscountAmount     float64                `json:"discount_amount"`
	FreeItemID         *int64                 `json:"free_item_id,omitempty"`        // 保持 int64 类型
	FreeItemName       *string                `json:"free_item_name,omitempty"`
	FreeItemQuantity   *float64               `json:"free_item_quantity,omitempty"`
	MinimumOrderAmount *float64               `json:"minimum_order_amount,omitempty"`
	Metadata           map[string]interface{} `json:"metadata"`
	CreatedAt          time.Time              `json:"created_at"`
}

// OrderPayment 数据传输对象
type OrderPayment struct {
	ID               ID                     `json:"id"`
	OrderID          ID                     `json:"order_id"`
	PaymentMethod     string                 `json:"payment_method"`      // 支付方式ID（存储为字符串）
	PaymentMethodName *string                `json:"payment_method_name,omitempty"` // 支付方式名称（通过join查询获取）
	PaymentReference  *string                `json:"payment_reference,omitempty"`
	Amount            float64                `json:"amount"`
	Status           PaymentStatus          `json:"status"`
	TransactionID    *string                `json:"transaction_id,omitempty"`
	Metadata         map[string]interface{} `json:"metadata"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
	CompletedAt      *time.Time             `json:"completed_at,omitempty"`
}

// Employee 员工数据传输对象
type Employee struct {
	ID         ID         `json:"id"`
	EmployeeNo string     `json:"employee_no"`
	Name       string     `json:"name"`
	Password   string     `json:"-"` // 密码不返回给前端
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at,omitempty"`
}

// EmployeeFilter 员工筛选参数
type EmployeeFilter struct {
	EmployeeNo *string    `json:"employee_no,omitempty"`
	Name       *string    `json:"name,omitempty"`
	DateFrom   *time.Time `json:"date_from,omitempty"`
	DateTo     *time.Time `json:"date_to,omitempty"`
}

// StaffCardType 员工卡类型
type StaffCardType string

const (
	StaffCardTypeEmployee StaffCardType = "employee"
	StaffCardTypeManager  StaffCardType = "manager"
	StaffCardTypeAdmin    StaffCardType = "admin"
)

// StaffCardStatus 员工卡状态
type StaffCardStatus string

const (
	StaffCardStatusActive    StaffCardStatus = "active"
	StaffCardStatusInactive  StaffCardStatus = "inactive"
	StaffCardStatusSuspended StaffCardStatus = "suspended"
	StaffCardStatusExpired   StaffCardStatus = "expired"
)

// StaffCard 员工卡数据传输对象
type StaffCard struct {
	ID          ID                     `json:"id"`
	CardNumber  string                 `json:"card_number"`
	UserID      string                 `json:"user_id"`         // UUID字符串
	StationID   *ID                    `json:"station_id,omitempty"`
	CardType    StaffCardType          `json:"card_type"`
	Status      StaffCardStatus        `json:"status"`
	ValidFrom   time.Time              `json:"valid_from"`
	ValidUntil  *time.Time             `json:"valid_until,omitempty"`
	Permissions map[string]interface{} `json:"permissions"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	DeletedAt   *time.Time             `json:"deleted_at,omitempty"`
	
	// 关联的用户信息（通过join查询获取）
	User        *AuthUser              `json:"user,omitempty"`
}

// AuthUser auth_db用户信息
type AuthUser struct {
	ID         string    `json:"id"`
	Username   string    `json:"username"`
	Email      string    `json:"email"`
	Phone      *string   `json:"phone,omitempty"`
	FullName   string    `json:"full_name"`
	Department *string   `json:"department,omitempty"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// StaffCardFilter 员工卡筛选参数
type StaffCardFilter struct {
	UserID      *string           `json:"user_id,omitempty"`
	StationID   *ID               `json:"station_id,omitempty"`
	CardType    *StaffCardType    `json:"card_type,omitempty"`
	Status      *StaffCardStatus  `json:"status,omitempty"`
	CardNumber  *string           `json:"card_number,omitempty"`
	DateFrom    *time.Time        `json:"date_from,omitempty"`
	DateTo      *time.Time        `json:"date_to,omitempty"`
	ValidOnly   *bool             `json:"valid_only,omitempty"`  // 只查询有效期内的卡
}

// IsValid 检查员工卡是否在指定时间有效
func (sc *StaffCard) IsValid(checkTime time.Time) bool {
	if sc.Status != StaffCardStatusActive {
		return false
	}
	
	if checkTime.Before(sc.ValidFrom) {
		return false
	}
	
	if sc.ValidUntil != nil && checkTime.After(*sc.ValidUntil) {
		return false
	}
	
	return true
}

// IsCurrentlyValid 检查员工卡当前是否有效
func (sc *StaffCard) IsCurrentlyValid() bool {
	return sc.IsValid(time.Now())
}

// HasStationAccess 检查员工卡是否有指定站点的访问权限
func (sc *StaffCard) HasStationAccess(stationID ID) bool {
	// 如果员工卡没有指定站点，表示可以在所有站点使用
	if sc.StationID == nil {
		return true
	}
	
	// 如果指定了站点，只能在该站点使用
	return *sc.StationID == stationID
}

// StaffCardValidationResult 员工卡验证结果
type StaffCardValidationResult struct {
	IsValid    bool   `json:"is_valid"`
	CardID     *ID    `json:"card_id,omitempty"`
	UserID     string `json:"user_id,omitempty"`
	Status     string `json:"status,omitempty"`
	Message    string `json:"message"`
	StaffCard  *StaffCard `json:"staff_card,omitempty"`
}

// FuelTransaction 数据传输对象
type FuelTransaction struct {
	ID                ID                     `json:"id"`
	TransactionNumber string                 `json:"transaction_number"`
	StationID         int64                  `json:"station_id"`              // 保持 int64 类型
	PumpID            string                 `json:"pump_id"`
	NozzleID          string                 `json:"nozzle_id"`
	FuelType          string                 `json:"fuel_type"`
	FuelGrade         string                 `json:"fuel_grade"`
	Tank              int                    `json:"tank"`
	UnitPrice         float64                `json:"unit_price"`
	Volume            float64                `json:"volume"`
	Amount            float64                `json:"amount"`
	TotalVolume       float64                `json:"total_volume"`
	TotalAmount       float64                `json:"total_amount"`
	Status            FuelTransactionStatus  `json:"status"`
	MemberCardID      *string                `json:"member_card_id,omitempty"`
	MemberID          *int64                 `json:"member_id,omitempty"`     // 保持 int64 类型
	EmployeeID        *ID                    `json:"employee_id,omitempty"`   // UUID 类型，兼容字段
	StaffCardID       *ID                    `json:"staff_card_id,omitempty"` // 新字段，员工卡ID
	ShiftID           *ID                    `json:"shift_id,omitempty"`      // 班次ID
	FCCTransactionID  *string                `json:"fcc_transaction_id,omitempty"`
	POSTerminalID     *string                `json:"pos_terminal_id,omitempty"`
	StartTotalizer            *float64               `json:"start_totalizer,omitempty"`
	EndTotalizer              *float64               `json:"end_totalizer,omitempty"`
	NozzleStartTime           *time.Time             `json:"nozzle_start_time,omitempty"`
	NozzleEndTime             *time.Time             `json:"nozzle_end_time,omitempty"`
	TotalizerContinuityStatus *string                `json:"totalizer_continuity_status,omitempty"` // 泵码连续性状态：normal/abnormal/unknown
	Metadata                  map[string]interface{} `json:"metadata"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	ProcessedAt       *time.Time             `json:"processed_at,omitempty"`
	CancelledAt       *time.Time             `json:"cancelled_at,omitempty"`
	
	// 关联的员工卡信息（通过join查询获取）
	StaffCard         *StaffCard             `json:"staff_card,omitempty"`
}

// FuelTransactionFull 完整的燃油交易信息结构（用于连表查询优化）
type FuelTransactionFull struct {
	// 基础燃油交易信息
	ID                     ID                     `json:"id"`
	TransactionNumber      string                 `json:"transaction_number"`
	StationID              int64                  `json:"station_id"`              // 保持 int64 类型
	PumpID                 string                 `json:"pump_id"`
	NozzleID               string                 `json:"nozzle_id"`
	FuelType               string                 `json:"fuel_type"`
	FuelGrade              string                 `json:"fuel_grade"`
	Tank                   int                    `json:"tank"`
	UnitPrice              float64                `json:"unit_price"`
	Volume                 float64                `json:"volume"`
	Amount                 float64                `json:"amount"`
	TotalVolume            float64                `json:"total_volume"`
	TotalAmount            float64                `json:"total_amount"`
	Status                 FuelTransactionStatus  `json:"status"`
	MemberCardID           *string                `json:"member_card_id"`
	MemberID               *int64                 `json:"member_id"`       // 保持 int64 类型
	EmployeeID             *ID                    `json:"employee_id"`     // UUID 类型
	StaffCardID            *ID                    `json:"staff_card_id"`
	ShiftID                *ID                    `json:"shift_id"`
	FCCTransactionID       *string                `json:"fcc_transaction_id"`
	POSTerminalID          *string                `json:"pos_terminal_id"`
	StartTotalizer         *float64               `json:"start_totalizer"`
	EndTotalizer           *float64               `json:"end_totalizer"`
	NozzleStartTime        *time.Time             `json:"nozzle_start_time"`
	NozzleEndTime          *time.Time             `json:"nozzle_end_time"`
	Metadata               map[string]interface{} `json:"metadata"`
	CreatedAt              time.Time              `json:"created_at"`
	UpdatedAt              time.Time              `json:"updated_at"`
	ProcessedAt            *time.Time             `json:"processed_at"`
	CancelledAt            *time.Time             `json:"cancelled_at"`
	
	// 扩展信息（通过连表查询获取）
	StationName            string                 `json:"station_name"`
	SiteCode               string                 `json:"site_code"`
	EmployeeName           string                 `json:"employee_name"`
	ShiftName              string                 `json:"shift_name"`
	CustomerName           string                 `json:"customer_name"`
	CustomerPhone          string                 `json:"customer_phone"`
	VehicleType            string                 `json:"vehicle_type"`
	LicensePlate           string                 `json:"license_plate"`
	PromotionName          string                 `json:"promotion_name"`
	OrderNumbers           string                 `json:"order_numbers"`
	PaymentMethods         string                 `json:"payment_methods"`
	FirstPaymentTime       *time.Time             `json:"first_payment_time"`
	
	// 折扣信息（新增）
	TotalDiscountAmount     float64               `json:"total_discount_amount"`      // 总折扣金额
	ItemDiscountAmount      float64               `json:"item_discount_amount"`       // 订单项级别折扣
	PromotionDiscountAmount float64               `json:"promotion_discount_amount"`  // 促销活动折扣
	OrderDiscountAmount     float64               `json:"order_discount_amount"`      // 订单级别折扣

	// 免费升数信息（新增）
	FreeLiter               float64               `json:"free_liter"`                 // 免费升数 (L)
	FreeLiterAmount         float64               `json:"free_liter_amount"`          // 免费升金额 (Rp)

	// 泵码连续性状态（新增）
	TotalizerContinuityStatus string              `json:"totalizer_continuity_status"` // 泵码连续性状态：normal/abnormal
}

// FuelTransactionOrderLink 数据传输对象
type FuelTransactionOrderLink struct {
	ID                ID                     `json:"id"`
	FuelTransactionID ID                     `json:"fuel_transaction_id"`
	OrderID           ID                     `json:"order_id"`
	AllocatedAmount   float64                `json:"allocated_amount"`
	Status            LinkStatus             `json:"status"`
	Metadata          map[string]interface{} `json:"metadata"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	DeactivatedAt     *time.Time             `json:"deactivated_at,omitempty"`
}

// Shift 班次数据传输对象
type Shift struct {
	ID          ID                     `json:"id"`
	ShiftNumber string                 `json:"shift_number"`
	StationID   int64                  `json:"station_id"`  // 保持 int64 类型
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	DeletedAt   *time.Time             `json:"deleted_at,omitempty"`
}

// 获取班次状态
func (s *Shift) GetStatus() ShiftStatus {
	if s.EndTime == nil {
		return ShiftStatusActive
	}
	return ShiftStatusClosed
}

// 判断班次是否处于活跃状态
func (s *Shift) IsActive() bool {
	return s.EndTime == nil
}

// ShiftTemplate 班次模板数据传输对象
type ShiftTemplate struct {
	ID        ID                     `json:"id" db:"id"`
	Name      string                 `json:"name" db:"name"`
	StationID ID                     `json:"station_id" db:"station_id"`
	StartTime string                 `json:"start_time" db:"start_time"`
	EndTime   string                 `json:"end_time" db:"end_time"`
	Status    string                 `json:"status" db:"status"`
	Metadata  map[string]interface{} `json:"metadata" db:"metadata"`
	CreatedAt time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt time.Time              `json:"updated_at" db:"updated_at"`
	DeletedAt *time.Time             `json:"deleted_at,omitempty" db:"deleted_at"`
}

// ShiftTemplateStatus 班次模板状态
type ShiftTemplateStatus string

// 班次模板状态常量
const (
	ShiftTemplateStatusActive   ShiftTemplateStatus = "active"
	ShiftTemplateStatusInactive ShiftTemplateStatus = "inactive"
)

// IsActive 判断班次模板是否处于活跃状态
func (t *ShiftTemplate) IsActive() bool {
	return t.Status == string(ShiftTemplateStatusActive)
}

// ShiftTemplateFilter 班次模板筛选参数
type ShiftTemplateFilter struct {
	StationID      *ID                  `json:"station_id,omitempty"`
	Status         *ShiftTemplateStatus `json:"status,omitempty"`
	Name           *string              `json:"name,omitempty"`
	IncludeDeleted *bool                `json:"include_deleted,omitempty"`
}

// EmployeeShift 员工班次数据传输对象
type EmployeeShift struct {
	ID         ID                     `json:"id" db:"id"`
	EmployeeID ID                     `json:"employee_id" db:"employee_id"`
	ShiftID    ID                     `json:"shift_id" db:"shift_id"`
	StationID  ID                     `json:"station_id" db:"station_id"`
	Date       time.Time              `json:"date" db:"date"`
	StartTime  time.Time              `json:"start_time" db:"start_time"`
	EndTime    *time.Time             `json:"end_time,omitempty" db:"end_time"`
	Status     string                 `json:"status" db:"status"`
	Notes      string                 `json:"notes" db:"notes"`
	Metadata   map[string]interface{} `json:"metadata" db:"metadata"`
	CreatedAt  time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at" db:"updated_at"`
	DeletedAt  *time.Time             `json:"deleted_at,omitempty" db:"deleted_at"`
}

// EmployeeShiftStatus 员工班次状态
type EmployeeShiftStatus string

// 员工班次状态常量
const (
	EmployeeShiftStatusScheduled  EmployeeShiftStatus = "scheduled"   // 已排班
	EmployeeShiftStatusCheckedIn  EmployeeShiftStatus = "checked_in"  // 已签到
	EmployeeShiftStatusCheckedOut EmployeeShiftStatus = "checked_out" // 已签退
	EmployeeShiftStatusAbsent     EmployeeShiftStatus = "absent"      // 缺勤
)

// CanCheckIn 判断员工班次是否可以签到
func (e *EmployeeShift) CanCheckIn() bool {
	return e.Status == string(EmployeeShiftStatusScheduled)
}

// CanCheckOut 判断员工班次是否可以签退
func (e *EmployeeShift) CanCheckOut() bool {
	return e.Status == string(EmployeeShiftStatusCheckedIn)
}

// EmployeeShiftFilter 员工班次筛选参数
type EmployeeShiftFilter struct {
	EmployeeID     *ID                  `json:"employee_id,omitempty"`
	ShiftID        *ID                  `json:"shift_id,omitempty"`
	StationID      *ID                  `json:"station_id,omitempty"`
	Status         *EmployeeShiftStatus `json:"status,omitempty"`
	DateFrom       *time.Time           `json:"date_from,omitempty"`
	DateTo         *time.Time           `json:"date_to,omitempty"`
	IncludeDeleted *bool                `json:"include_deleted,omitempty"`
}
