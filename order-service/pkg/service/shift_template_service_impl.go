package service

import (
	"context"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/errors"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// ShiftTemplateServiceImpl 实现ShiftTemplateService接口
type ShiftTemplateServiceImpl struct {
	templateRepo repository.ShiftTemplateRepository
}

// NewShiftTemplateService 创建一个新的ShiftTemplateService实例
func NewShiftTemplateService(templateRepo repository.ShiftTemplateRepository) ShiftTemplateService {
	return &ShiftTemplateServiceImpl{
		templateRepo: templateRepo,
	}
}

// CreateTemplate 创建班次模板
func (s *ShiftTemplateServiceImpl) CreateTemplate(ctx context.Context, template repository.ShiftTemplate) (repository.ShiftTemplate, error) {
	// 设置默认值
	template.CreatedAt = time.Now()
	template.UpdatedAt = template.CreatedAt

	// 如果没有设置状态，默认为活跃状态
	if template.Status == "" {
		template.Status = string(repository.ShiftTemplateStatusActive)
	}

	// 如果没有设置元数据，初始化为空map
	if template.Metadata == nil {
		template.Metadata = make(map[string]interface{})
	}

	// 验证时间格式是否正确 (HH:MM)
	if !isValidTimeFormat(template.StartTime) || !isValidTimeFormat(template.EndTime) {
		return repository.ShiftTemplate{}, fmt.Errorf("无效的时间格式，应为HH:MM格式")
	}

	// 保存到数据库
	err := s.templateRepo.CreateShiftTemplate(ctx, &template)
	if err != nil {
		return repository.ShiftTemplate{}, fmt.Errorf("创建班次模板失败: %w", err)
	}

	return template, nil
}

// UpdateTemplate 更新班次模板
func (s *ShiftTemplateServiceImpl) UpdateTemplate(ctx context.Context, template repository.ShiftTemplate) (repository.ShiftTemplate, error) {
	// 更新时间
	template.UpdatedAt = time.Now()

	// 验证时间格式是否正确
	if !isValidTimeFormat(template.StartTime) || !isValidTimeFormat(template.EndTime) {
		return repository.ShiftTemplate{}, fmt.Errorf("无效的时间格式，应为HH:MM格式")
	}

	// 先检查template是否存在
	existingTemplate, err := s.templateRepo.GetShiftTemplateByID(ctx, template.ID)
	if err != nil {
		if err == repository.ErrNotFound {
			return repository.ShiftTemplate{}, errors.ShiftTemplateNotFoundError(template.ID.String(), err)
		}
		return repository.ShiftTemplate{}, fmt.Errorf("获取班次模板失败: %w", err)
	}

	// 保留原始创建时间
	template.CreatedAt = existingTemplate.CreatedAt

	// 更新到数据库
	err = s.templateRepo.UpdateShiftTemplate(ctx, &template)
	if err != nil {
		return repository.ShiftTemplate{}, fmt.Errorf("更新班次模板失败: %w", err)
	}

	return template, nil
}

// DeleteTemplate 删除班次模板（软删除）
func (s *ShiftTemplateServiceImpl) DeleteTemplate(ctx context.Context, templateID repository.ID) error {
	// 检查模板是否存在
	_, err := s.templateRepo.GetShiftTemplateByID(ctx, templateID)
	if err != nil {
		if err == repository.ErrNotFound {
			return errors.ShiftTemplateNotFoundError(templateID.String(), err)
		}
		return fmt.Errorf("获取班次模板失败: %w", err)
	}

	// 执行软删除
	err = s.templateRepo.DeleteShiftTemplate(ctx, templateID)
	if err != nil {
		return fmt.Errorf("删除班次模板失败: %w", err)
	}

	return nil
}

// GetTemplateByID 根据ID获取班次模板
func (s *ShiftTemplateServiceImpl) GetTemplateByID(ctx context.Context, templateID repository.ID) (repository.ShiftTemplate, error) {
	template, err := s.templateRepo.GetShiftTemplateByID(ctx, templateID)
	if err != nil {
		if err == repository.ErrNotFound {
			return repository.ShiftTemplate{}, errors.ShiftTemplateNotFoundError(templateID.String(), err)
		}
		return repository.ShiftTemplate{}, fmt.Errorf("获取班次模板失败: %w", err)
	}

	return *template, nil
}

// ListTemplates 列出班次模板
func (s *ShiftTemplateServiceImpl) ListTemplates(ctx context.Context, filter repository.ShiftTemplateFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.ShiftTemplate, int, error) {
	templates, total, err := s.templateRepo.ListShiftTemplates(ctx, &filter, &pagination, &sort)
	if err != nil {
		return nil, 0, fmt.Errorf("列出班次模板失败: %w", err)
	}

	// 转换为非指针类型的切片
	result := make([]repository.ShiftTemplate, len(templates))
	for i, t := range templates {
		result[i] = *t
	}

	return result, total, nil
}

// GetActiveTemplatesForStation 获取指定加油站的所有活跃班次模板
func (s *ShiftTemplateServiceImpl) GetActiveTemplatesForStation(ctx context.Context, stationID repository.ID) ([]repository.ShiftTemplate, error) {
	templates, err := s.templateRepo.GetActiveShiftTemplatesForStation(ctx, stationID)
	if err != nil {
		return nil, fmt.Errorf("获取活跃班次模板失败: %w", err)
	}

	// 转换为非指针类型的切片
	result := make([]repository.ShiftTemplate, len(templates))
	for i, t := range templates {
		result[i] = *t
	}

	return result, nil
}

// ActivateTemplate 激活班次模板
func (s *ShiftTemplateServiceImpl) ActivateTemplate(ctx context.Context, templateID repository.ID) error {
	// 获取模板
	template, err := s.templateRepo.GetShiftTemplateByID(ctx, templateID)
	if err != nil {
		if err == repository.ErrNotFound {
			return errors.ShiftTemplateNotFoundError(templateID.String(), err)
		}
		return fmt.Errorf("获取班次模板失败: %w", err)
	}

	// 更新状态
	template.Status = string(repository.ShiftTemplateStatusActive)
	template.UpdatedAt = time.Now()

	// 保存到数据库
	err = s.templateRepo.UpdateShiftTemplate(ctx, template)
	if err != nil {
		return fmt.Errorf("激活班次模板失败: %w", err)
	}

	return nil
}

// DeactivateTemplate 停用班次模板
func (s *ShiftTemplateServiceImpl) DeactivateTemplate(ctx context.Context, templateID repository.ID) error {
	// 获取模板
	template, err := s.templateRepo.GetShiftTemplateByID(ctx, templateID)
	if err != nil {
		if err == repository.ErrNotFound {
			return errors.ShiftTemplateNotFoundError(templateID.String(), err)
		}
		return fmt.Errorf("获取班次模板失败: %w", err)
	}

	// 更新状态
	template.Status = string(repository.ShiftTemplateStatusInactive)
	template.UpdatedAt = time.Now()

	// 保存到数据库
	err = s.templateRepo.UpdateShiftTemplate(ctx, template)
	if err != nil {
		return fmt.Errorf("停用班次模板失败: %w", err)
	}

	return nil
}

// 辅助函数

// isValidTimeFormat 验证时间格式是否为HH:MM
func isValidTimeFormat(timeStr string) bool {
	_, err := time.Parse("15:04", timeStr)
	return err == nil
}
