package service

import (
	"context"
	"fmt"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// StationServiceImpl 站点服务实现
type StationServiceImpl struct {
	stationRepo repository.StationRepository
}

// NewStationService 创建站点服务实例
func NewStationService(stationRepo repository.StationRepository) StationService {
	return &StationServiceImpl{
		stationRepo: stationRepo,
	}
}

// GetStationByID 根据ID获取站点信息
func (s *StationServiceImpl) GetStationByID(ctx context.Context, stationID int64) (*repository.Station, error) {
	// 将 int64 转换为 repository.ID
	stationUUID := repository.IDFromInt64(stationID)
	station, err := s.stationRepo.GetStationByID(ctx, stationUUID)
	if err != nil {
		return nil, fmt.Errorf("获取站点信息失败: %w", err)
	}
	return station, nil
}

// GetStationName 根据ID获取站点名称
func (s *StationServiceImpl) GetStationName(ctx context.Context, stationID int64) (string, error) {
	// 将 int64 转换为 repository.ID
	stationUUID := repository.IDFromInt64(stationID)
	stationName, err := s.stationRepo.GetStationName(ctx, stationUUID)
	if err != nil {
		return "", fmt.Errorf("获取站点名称失败: %w", err)
	}
	return stationName, nil
}

// GetStationsBatch 批量获取站点信息
func (s *StationServiceImpl) GetStationsBatch(ctx context.Context, stationIDs []int64) (map[int64]*repository.Station, error) {
	// 将 []int64 转换为 []repository.ID
	stationUUIDs := make([]repository.ID, len(stationIDs))
	for i, id := range stationIDs {
		stationUUIDs[i] = repository.IDFromInt64(id)
	}

	stations, err := s.stationRepo.GetStationsBatch(ctx, stationUUIDs)
	if err != nil {
		return nil, fmt.Errorf("批量获取站点信息失败: %w", err)
	}

	// 将结果转换回 map[int64]*repository.Station
	result := make(map[int64]*repository.Station)
	for uuid, station := range stations {
		// 这里需要一个从 UUID 转换回 int64 的方法
		// 暂时使用简单的映射，实际应用中需要维护映射表
		for i, originalID := range stationIDs {
			if stationUUIDs[i] == uuid {
				result[originalID] = station
				break
			}
		}
	}

	return result, nil
}