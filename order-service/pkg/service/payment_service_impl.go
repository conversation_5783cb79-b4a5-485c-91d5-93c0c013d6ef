package service

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// PaymentServiceImpl 支付服务实现
type PaymentServiceImpl struct {
	paymentDB *database.Database
}

// NewPaymentService 创建支付服务实例
func NewPaymentService(paymentDB *database.Database) PaymentService {
	return &PaymentServiceImpl{
		paymentDB: paymentDB,
	}
}

// GetPaymentsByOrderIDs 根据订单ID列表获取支付信息
func (s *PaymentServiceImpl) GetPaymentsByOrderIDs(ctx context.Context, orderIDs []string) ([]repository.PaymentInfo, error) {
	if len(orderIDs) == 0 {
		return []repository.PaymentInfo{}, nil
	}

	// 构建查询条件
	query := `
		SELECT 
			id, order_id, payment_number, amount, currency, payment_type, 
			payment_method, status, gateway_type, gateway_order_no, gateway_response,
			customer_id, customer_name, station_id, terminal_id, operator_id,
			created_at, updated_at, paid_at, expires_at, metadata
		FROM payments 
		WHERE order_id = ANY($1) AND status = 'SUCCESS'
		ORDER BY created_at DESC
	`

	rows, err := s.paymentDB.GetPool().Query(ctx, query, orderIDs)
	if err != nil {
		return nil, fmt.Errorf("查询支付信息失败: %w", err)
	}
	defer rows.Close()

	var payments []repository.PaymentInfo
	for rows.Next() {
		var payment repository.PaymentInfo
		var paymentMethod sql.NullInt64
		var gatewayType, gatewayOrderNo, gatewayResponse sql.NullString
		var customerID, customerName, terminalID, operatorID sql.NullString
		var paidAt, expiresAt sql.NullTime
		var metadata sql.NullString

		err := rows.Scan(
			&payment.ID,
			&payment.OrderID,
			&payment.PaymentNumber,
			&payment.Amount,
			&payment.Currency,
			&payment.PaymentType,
			&paymentMethod,
			&payment.Status,
			&gatewayType,
			&gatewayOrderNo,
			&gatewayResponse,
			&customerID,
			&customerName,
			&payment.StationID,
			&terminalID,
			&operatorID,
			&payment.CreatedAt,
			&payment.UpdatedAt,
			&paidAt,
			&expiresAt,
			&metadata,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描支付信息失败: %w", err)
		}

		// 处理可空字段
		// TODO: 修复 paymentMethod 字段的 UUID 转换
		// if paymentMethod.Valid {
		// 	methodID := repository.ID(paymentMethod.Int64)
		// 	payment.PaymentMethod = &methodID
		// }
		if gatewayType.Valid {
			payment.GatewayType = &gatewayType.String
		}
		if gatewayOrderNo.Valid {
			payment.GatewayOrderNo = &gatewayOrderNo.String
		}
		if gatewayResponse.Valid {
			payment.GatewayResponse = &gatewayResponse.String
		}
		if customerID.Valid {
			payment.CustomerID = &customerID.String
		}
		if customerName.Valid {
			payment.CustomerName = &customerName.String
		}
		if terminalID.Valid {
			payment.TerminalID = &terminalID.String
		}
		if operatorID.Valid {
			payment.OperatorID = &operatorID.String
		}
		if paidAt.Valid {
			payment.PaidAt = &paidAt.Time
		}
		if expiresAt.Valid {
			payment.ExpiresAt = &expiresAt.Time
		}
		if metadata.Valid {
			payment.Metadata = &metadata.String
		}

		payments = append(payments, payment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历支付信息失败: %w", err)
	}

	return payments, nil
}

// GetPaymentsByStationAndTimeRange 根据站点和时间范围获取支付信息
func (s *PaymentServiceImpl) GetPaymentsByStationAndTimeRange(ctx context.Context, stationID repository.ID, startTime, endTime time.Time) ([]repository.PaymentInfo, error) {
	query := `
		SELECT 
			id, order_id, payment_number, amount, currency, payment_type, 
			payment_method, status, gateway_type, gateway_order_no, gateway_response,
			customer_id, customer_name, station_id, terminal_id, operator_id,
			created_at, updated_at, paid_at, expires_at, metadata
		FROM payments 
		WHERE station_id = $1 
		  AND created_at >= $2 
		  AND created_at <= $3 
		  AND status = 'SUCCESS'
		ORDER BY created_at DESC
	`

	rows, err := s.paymentDB.GetPool().Query(ctx, query, stationID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("查询支付信息失败: %w", err)
	}
	defer rows.Close()

	var payments []repository.PaymentInfo
	for rows.Next() {
		var payment repository.PaymentInfo
		var paymentMethod sql.NullInt64
		var gatewayType, gatewayOrderNo, gatewayResponse sql.NullString
		var customerID, customerName, terminalID, operatorID sql.NullString
		var paidAt, expiresAt sql.NullTime
		var metadata sql.NullString

		err := rows.Scan(
			&payment.ID,
			&payment.OrderID,
			&payment.PaymentNumber,
			&payment.Amount,
			&payment.Currency,
			&payment.PaymentType,
			&paymentMethod,
			&payment.Status,
			&gatewayType,
			&gatewayOrderNo,
			&gatewayResponse,
			&customerID,
			&customerName,
			&payment.StationID,
			&terminalID,
			&operatorID,
			&payment.CreatedAt,
			&payment.UpdatedAt,
			&paidAt,
			&expiresAt,
			&metadata,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描支付信息失败: %w", err)
		}

		// 处理可空字段
		// TODO: 修复 paymentMethod 字段的 UUID 转换
		// if paymentMethod.Valid {
		// 	methodID := repository.ID(paymentMethod.Int64)
		// 	payment.PaymentMethod = &methodID
		// }
		if gatewayType.Valid {
			payment.GatewayType = &gatewayType.String
		}
		if gatewayOrderNo.Valid {
			payment.GatewayOrderNo = &gatewayOrderNo.String
		}
		if gatewayResponse.Valid {
			payment.GatewayResponse = &gatewayResponse.String
		}
		if customerID.Valid {
			payment.CustomerID = &customerID.String
		}
		if customerName.Valid {
			payment.CustomerName = &customerName.String
		}
		if terminalID.Valid {
			payment.TerminalID = &terminalID.String
		}
		if operatorID.Valid {
			payment.OperatorID = &operatorID.String
		}
		if paidAt.Valid {
			payment.PaidAt = &paidAt.Time
		}
		if expiresAt.Valid {
			payment.ExpiresAt = &expiresAt.Time
		}
		if metadata.Valid {
			payment.Metadata = &metadata.String
		}

		payments = append(payments, payment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历支付信息失败: %w", err)
	}

	return payments, nil
}

// GetPaymentMethods 获取支付方式配置
func (s *PaymentServiceImpl) GetPaymentMethods(ctx context.Context) ([]repository.PaymentMethod, error) {
	query := `
		SELECT 
			id, type, name, display_name, description, icon, gateway_type, gateway_config,
			enabled, min_amount, max_amount, daily_limit, fee_type, fee_value,
			available_time, allowed_stations, sort_order, group_name,
			created_at, updated_at, deleted_at
		FROM payment_methods 
		WHERE deleted_at IS NULL
		ORDER BY sort_order, id
	`

	rows, err := s.paymentDB.GetPool().Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询支付方式失败: %w", err)
	}
	defer rows.Close()

	var methods []repository.PaymentMethod
	for rows.Next() {
		var method repository.PaymentMethod
		var description, icon, gatewayConfig sql.NullString
		var minAmount, maxAmount, dailyLimit, feeValue sql.NullFloat64
		var feeType, availableTime, allowedStations, groupName sql.NullString
		var sortOrder sql.NullInt32
		var deletedAt sql.NullTime

		err := rows.Scan(
			&method.ID,
			&method.Type,
			&method.Name,
			&method.DisplayName,
			&description,
			&icon,
			&method.GatewayType,
			&gatewayConfig,
			&method.Enabled,
			&minAmount,
			&maxAmount,
			&dailyLimit,
			&feeType,
			&feeValue,
			&availableTime,
			&allowedStations,
			&sortOrder,
			&groupName,
			&method.CreatedAt,
			&method.UpdatedAt,
			&deletedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描支付方式失败: %w", err)
		}

		// 处理可空字段
		if description.Valid {
			method.Description = &description.String
		}
		if icon.Valid {
			method.Icon = &icon.String
		}
		if gatewayConfig.Valid {
			method.GatewayConfig = &gatewayConfig.String
		}
		if minAmount.Valid {
			method.MinAmount = &minAmount.Float64
		}
		if maxAmount.Valid {
			method.MaxAmount = &maxAmount.Float64
		}
		if dailyLimit.Valid {
			method.DailyLimit = &dailyLimit.Float64
		}
		if feeType.Valid {
			method.FeeType = &feeType.String
		}
		if feeValue.Valid {
			method.FeeValue = &feeValue.Float64
		}
		if availableTime.Valid {
			method.AvailableTime = &availableTime.String
		}
		if allowedStations.Valid {
			method.AllowedStations = &allowedStations.String
		}
		if sortOrder.Valid {
			sortOrderInt := int(sortOrder.Int32)
			method.SortOrder = &sortOrderInt
		}
		if groupName.Valid {
			method.GroupName = &groupName.String
		}
		if deletedAt.Valid {
			method.DeletedAt = &deletedAt.Time
		}

		methods = append(methods, method)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历支付方式失败: %w", err)
	}

	return methods, nil
} 