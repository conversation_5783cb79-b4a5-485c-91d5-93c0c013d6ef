package service

import (
	"context"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/errors"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/utils"
)

// 使用共享的时区工具

// ShiftServiceImpl 实现ShiftService接口
type ShiftServiceImpl struct {
	shiftRepo           repository.ShiftRepository
	fuelTransactionRepo repository.FuelTransactionRepository
	shiftSummaryRepo    repository.ShiftSummaryRepository
	stationService      StationService
}

// NewShiftService 创建一个新的ShiftService实例
func NewShiftService(shiftRepo repository.ShiftRepository, fuelTransactionRepo repository.FuelTransactionRepository, shiftSummaryRepo repository.ShiftSummaryRepository, stationService StationService) ShiftService {
	return &ShiftServiceImpl{
		shiftRepo:           shiftRepo,
		fuelTransactionRepo: fuelTransactionRepo,
		shiftSummaryRepo:    shiftSummaryRepo,
		stationService:      stationService,
	}
}

// StartShift 开始一个新的班次
// 如果当前站点已有活跃班次，则返回错误
// 新班次的开始时间将使用上一个班次的结束时间，如果没有上一个班次则使用当前时间
func (s *ShiftServiceImpl) StartShift(ctx context.Context, stationID int64, metadata map[string]interface{}) (repository.Shift, error) {
	// 检查是否已有活跃班次
	_, err := s.shiftRepo.FindActiveShiftByStationID(ctx, stationID)
	if err == nil {
		// 已存在活跃班次，返回错误
		return repository.Shift{}, errors.ShiftAlreadyActiveError(stationID.String(), nil)
	}

	// 错误不是"未找到活跃班次"，返回该错误
	if !isNoActiveShiftError(err) {
		return repository.Shift{}, fmt.Errorf("检查活跃班次时发生错误: %w", err)
	}

	// 确定新班次的开始时间
	var startTime time.Time
	lastShift, err := s.shiftRepo.FindLastCompletedShiftByStationID(ctx, stationID)
	if err != nil || lastShift == nil || lastShift.EndTime == nil {
		// 如果没有找到上一个班次或上一个班次没有结束时间，使用当前Jakarta时间
		startTime = time.Now().In(utils.JakartaLocation)
	} else {
		// 使用上一个班次的结束时间作为新班次的开始时间
		startTime = *lastShift.EndTime
	}

	// 生成唯一的班次编号，使用新的生成规则
	shiftNumber, err := s.generateShiftNumber(ctx, stationID)
	if err != nil {
		return repository.Shift{}, fmt.Errorf("生成班次编号失败: %w", err)
	}

	// 创建新班次
	shift := repository.Shift{
		ShiftNumber: shiftNumber,
		StationID:   stationID,  // 直接使用 int64 类型
		StartTime:   startTime,  // 使用计算出的开始时间
		EndTime:     nil,        // 设置为nil表示班次正在进行中
		Metadata:    metadata,
	}

	// 保存到数据库
	createdShift, err := s.shiftRepo.CreateShift(ctx, &shift)
	if err != nil {
		return repository.Shift{}, fmt.Errorf("创建班次失败: %w", err)
	}

	return *createdShift, nil
}

// EndShift 结束当前活跃班次
// 如果当前站点没有活跃班次，则返回错误
// 如果有未处理的交易，则返回错误
func (s *ShiftServiceImpl) EndShift(ctx context.Context, stationID int64) (repository.Shift, error) {
	// 查找当前站点的活跃班次
	currentShift, err := s.shiftRepo.FindActiveShiftByStationIDWithLock(ctx, stationID)
	if err != nil {
		// 如果是未找到活跃班次的错误，返回NoActiveShiftError
		if isNoActiveShiftError(err) {
			return repository.Shift{}, errors.NoActiveShiftError(stationID.String(), err)
		}
		// 其他错误，包装后返回
		return repository.Shift{}, fmt.Errorf("查询活跃班次时发生错误: %w", err)
	}

	// 检查是否有未处理的交易
	// 创建查询条件：站点ID、开始时间>=当前班次开始时间、状态为待处理
	status := repository.FuelTransactionStatusPending
	filter := repository.FuelTransactionFilter{
		StationID: &stationID,  // 直接使用 int64 类型
		Status:    &status,
		DateFrom:  &currentShift.StartTime,
	}

	// 查询未处理的交易
	pagination := repository.Pagination{Page: 1, Limit: 1}
	sort := repository.SortOrder{Field: "created_at", Direction: "asc"}
	_, count, err := s.fuelTransactionRepo.List(ctx, filter, pagination, sort)
	if err != nil {
		return repository.Shift{}, fmt.Errorf("查询未处理燃油交易时发生错误: %w", err)
	}

	// 如果有未处理的交易，则返回错误
	if count > 0 {
		return repository.Shift{}, errors.CannotEndShiftWithPendingTransactionsError(stationID.String(), nil)
	}

	// 结束班次，设置结束时间为当前Jakarta时间
	now := time.Now().In(utils.JakartaLocation)
	updatedShift, err := s.shiftRepo.UpdateShiftEndTime(ctx, currentShift.ID, now)
	if err != nil {
		return repository.Shift{}, fmt.Errorf("更新班次结束时间失败: %w", err)
	}

	// 异步生成班结小票汇总数据
	if s.shiftSummaryRepo != nil {
		go func() {
			// 使用新的上下文，避免原上下文被取消影响班结数据生成
			summaryCtx := context.Background()

			// 使用默认配置生成班结汇总
			options := repository.DefaultGenerateOptions()
			options.OnProgress = func(table string, progress float64) {
				fmt.Printf("班次 %d %s 明细生成进度: %.1f%%\n", updatedShift.ID, table, progress*100)
			}
			options.OnError = func(table string, err error) {
				fmt.Printf("班次 %d %s 明细生成失败: %v\n", updatedShift.ID, table, err)
			}

			// 生成班结汇总数据
			if _, err := s.shiftSummaryRepo.GenerateShiftSummary(summaryCtx, updatedShift.ID, options); err != nil {
				// 记录错误日志，但不影响班次结束流程
				fmt.Printf("班次 %d 班结汇总生成失败: %v\n", updatedShift.ID, err)
			} else {
				fmt.Printf("班次 %d 班结汇总生成完成\n", updatedShift.ID)
			}
		}()
	}

	return *updatedShift, nil
}

// GetCurrentShift 获取当前站点的活跃班次
// 如果当前站点没有活跃班次，则返回错误
func (s *ShiftServiceImpl) GetCurrentShift(ctx context.Context, stationID int64) (repository.Shift, error) {
	// 调用仓库层方法查询当前站点的活跃班次
	shift, err := s.shiftRepo.FindActiveShiftByStationID(ctx, stationID)
	if err != nil {
		// 如果是未找到活跃班次的错误，返回NoActiveShiftError
		if isNoActiveShiftError(err) {
			return repository.Shift{}, errors.NoActiveShiftError(stationID.String(), err)
		}
		// 其他错误，包装后返回
		return repository.Shift{}, fmt.Errorf("查询活跃班次时发生错误: %w", err)
	}

	return *shift, nil
}

// EnsureShiftStarted 确保当前站点有活跃班次
// 如果没有活跃班次，则自动创建一个新的班次
func (s *ShiftServiceImpl) EnsureShiftStarted(ctx context.Context, stationID int64) (repository.Shift, bool, error) {
	// 检查是否已有活跃班次
	shift, err := s.shiftRepo.FindActiveShiftByStationID(ctx, stationID)
	if err == nil {
		// 已存在活跃班次，返回该班次和created=false
		return *shift, false, nil
	}

	// 如果错误不是"未找到活跃班次"，返回该错误
	if !isNoActiveShiftError(err) {
		return repository.Shift{}, false, fmt.Errorf("检查活跃班次时发生错误: %w", err)
	}

	// 没有活跃班次，自动创建一个新班次
	metadata := map[string]interface{}{
		"auto_created": true,
		"reason":       "system_ensure_shift_started",
	}

	// 调用StartShift方法创建新班次
	createdShift, err := s.StartShift(ctx, stationID, metadata)
	if err != nil {
		return repository.Shift{}, false, fmt.Errorf("自动创建班次失败: %w", err)
	}

	// 返回创建的班次和created=true
	return createdShift, true, nil
}

// ListShifts 列出符合筛选条件的班次
func (s *ShiftServiceImpl) ListShifts(ctx context.Context, filter repository.ShiftFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.Shift, int, error) {
	// 调用仓库层方法查询班次列表
	shifts, total, err := s.shiftRepo.ListShifts(ctx, &filter, &pagination, &sort)
	if err != nil {
		return nil, 0, fmt.Errorf("查询班次列表失败: %w", err)
	}

	// 转换返回类型
	result := make([]repository.Shift, len(shifts))
	for i, shift := range shifts {
		result[i] = *shift
	}

	return result, total, nil
}

// GetShift 根据ID获取班次
func (s *ShiftServiceImpl) GetShift(ctx context.Context, shiftID repository.ID) (repository.Shift, error) {
	// 调用仓库层方法获取班次
	shift, err := s.shiftRepo.GetShiftByID(ctx, shiftID)
	if err != nil {
		// 如果是未找到班次的错误，返回ShiftNotFoundError
		if fmt.Sprint(err) == fmt.Sprintf("未找到班次: %s", shiftID.String()) {
			return repository.Shift{}, errors.ShiftNotFoundError(shiftID.String(), err)
		}
		// 其他错误，包装后返回
		return repository.Shift{}, fmt.Errorf("获取班次失败: %w", err)
	}

	return *shift, nil
}

// GetShiftByNumber 根据班次编号获取班次
func (s *ShiftServiceImpl) GetShiftByNumber(ctx context.Context, shiftNumber string) (repository.Shift, error) {
	// 调用仓库层方法获取班次
	shift, err := s.shiftRepo.GetShiftByNumber(ctx, shiftNumber)
	if err != nil {
		// 如果是未找到班次的错误，返回ShiftNotFoundError
		if fmt.Sprint(err) == fmt.Sprintf("未找到班次: %s", shiftNumber) {
			return repository.Shift{}, errors.ShiftNotFoundError(shiftNumber, err)
		}
		// 其他错误，包装后返回
		return repository.Shift{}, fmt.Errorf("获取班次失败: %w", err)
	}

	return *shift, nil
}

// SoftDeleteShift 软删除班次
// 如果班次处于活跃状态，则返回错误
func (s *ShiftServiceImpl) SoftDeleteShift(ctx context.Context, shiftID repository.ID) error {
	// 先获取班次信息，确认班次存在且不处于活跃状态
	shift, err := s.shiftRepo.GetShiftByID(ctx, shiftID)
	if err != nil {
		// 如果是未找到班次的错误，返回ShiftNotFoundError
		if fmt.Sprint(err) == fmt.Sprintf("未找到班次: %s", shiftID.String()) {
			return errors.ShiftNotFoundError(shiftID.String(), err)
		}
		return fmt.Errorf("获取班次信息失败: %w", err)
	}

	// 检查班次是否处于活跃状态
	if shift.IsActive() {
		return errors.CannotDeleteActiveShiftError(shiftID.String(), nil)
	}

	// 执行软删除操作
	err = s.shiftRepo.SoftDeleteShift(ctx, shiftID)
	if err != nil {
		return fmt.Errorf("软删除班次失败: %w", err)
	}

	return nil
}

// RestoreShift 恢复已删除的班次
func (s *ShiftServiceImpl) RestoreShift(ctx context.Context, shiftID repository.ID) error {
	// 这里直接调用仓库层的恢复方法
	// 注意：由于GetShift只能获取未删除的班次，所以我们不能先调用GetShift来确认班次存在
	err := s.shiftRepo.RestoreShift(ctx, shiftID)
	if err != nil {
		return fmt.Errorf("恢复班次失败: %w", err)
	}

	return nil
}

// 生成唯一的班次编号
// 新格式: SHIFT-{SiteCode}-{YYMMDD}-{number_of_shifts}
func (s *ShiftServiceImpl) generateShiftNumber(ctx context.Context, stationID int64) (string, error) {
	// 获取站点的site code
	station, err := s.stationService.GetStationByID(ctx, stationID)
	if err != nil {
		return "", fmt.Errorf("获取站点信息失败: %w", err)
	}

	// 使用雅加达时间
	now := time.Now().In(utils.JakartaLocation)
	dateStr := now.Format("060102") // YYMMDD格式

	// 获取当天已有的班次数量
	count, err := s.shiftRepo.GetDailyShiftCount(ctx, stationID, now)
	if err != nil {
		return "", fmt.Errorf("获取当日班次数量失败: %w", err)
	}

	// 新班次序号是当前数量+1，格式化为两位数字
	nextShiftNumber := count + 1
	shiftSequence := fmt.Sprintf("%02d", nextShiftNumber)

	return fmt.Sprintf("SHIFT-%s-%s-%s", station.SiteCode, dateStr, shiftSequence), nil
}

// 旧的生成函数，保留用于向后兼容
func generateShiftNumber(stationID int64) string {
	timestamp := time.Now().In(utils.JakartaLocation).Format("20060102150405")
	return fmt.Sprintf("SHIFT-%d-%s", stationID, timestamp)
}

// ShiftNumberGenerator 班次编号生成器函数类型
type ShiftNumberGenerator func(stationID int64) string

// defaultShiftNumberGenerator 默认的班次编号生成器
var defaultShiftNumberGenerator ShiftNumberGenerator = generateShiftNumber

// GetShiftNumberGenerator 获取当前使用的班次编号生成器
func GetShiftNumberGenerator() ShiftNumberGenerator {
	return defaultShiftNumberGenerator
}

// SetShiftNumberGenerator 设置班次编号生成器
func SetShiftNumberGenerator(generator ShiftNumberGenerator) {
	defaultShiftNumberGenerator = generator
}

// isNoActiveShiftError 判断错误是否为"未找到活跃班次"错误
func isNoActiveShiftError(err error) bool {
	return err == repository.ErrNotFound
}

// GetShiftReport 获取班次报表数据
// 根据班次ID生成完整的班次报表，包含支付汇总、油品汇总、非油品汇总等
func (s *ShiftServiceImpl) GetShiftReport(ctx context.Context, shiftID repository.ID, options repository.ShiftReportOptions) (repository.ShiftReportDTO, error) {
	// 首先获取班次基本信息
	shift, err := s.GetShift(ctx, shiftID)
	if err != nil {
		return repository.ShiftReportDTO{}, fmt.Errorf("获取班次信息失败: %w", err)
	}

	// 检查班次是否已结束
	if shift.EndTime == nil {
		return repository.ShiftReportDTO{}, fmt.Errorf("班次尚未结束，无法生成报表")
	}

	// 尝试从汇总表获取数据
	if s.shiftSummaryRepo != nil {
		summary, err := s.shiftSummaryRepo.GetShiftSummary(ctx, shiftID)
		if err == nil {
			// 从汇总表构建报表数据
			return s.buildReportFromSummary(ctx, shift, *summary, options)
		}
		// 如果汇总表没有数据，继续使用实时计算
	}

	// 实时计算报表数据
	return s.buildReportFromRealtime(ctx, shift, options)
}

// GetShiftReceipt 获取班次小票格式数据
// 根据班次ID生成小票格式的数据，用于打印或显示
func (s *ShiftServiceImpl) GetShiftReceipt(ctx context.Context, shiftID repository.ID, options repository.ShiftReportOptions) (repository.ShiftReceiptDTO, error) {
	// 获取报表数据
	reportData, err := s.GetShiftReport(ctx, shiftID, options)
	if err != nil {
		return repository.ShiftReceiptDTO{}, fmt.Errorf("获取班次报表数据失败: %w", err)
	}

	// 转换为小票格式
	return s.convertToReceiptFormat(reportData, options)
}

// buildReportFromSummary 从汇总表数据构建报表
func (s *ShiftServiceImpl) buildReportFromSummary(ctx context.Context, shift repository.Shift, summary repository.ShiftSummaryDTO, options repository.ShiftReportOptions) (repository.ShiftReportDTO, error) {
	// 构建班次基本信息
	shiftInfo := s.buildShiftInfo(ctx, shift, options)

	// 构建支付方式汇总
	paymentSummary := s.buildPaymentSummaryFromSummary(ctx, shift.ID, summary)

	// 构建油品汇总
	fuelSummary := s.buildFuelSummaryFromSummary(ctx, shift.ID, summary)

	// 构建非油品汇总
	merchandiseSummary := s.buildMerchandiseSummaryFromSummary(ctx, shift.ID, summary)

	// 构建TERA分类汇总
	teraSummary := s.buildTeraSummaryFromSummary(ctx, shift.ID, summary)

	// 构建小票信息
	receiptInfo := s.buildReceiptInfo(shift, options)

	return repository.ShiftReportDTO{
		ShiftInfo:          shiftInfo,
		PaymentSummary:     paymentSummary,
		FuelSummary:        fuelSummary,
		MerchandiseSummary: merchandiseSummary,
		TeraSummary:        teraSummary,
		ReceiptInfo:        receiptInfo,
	}, nil
}

// buildReportFromRealtime 实时计算报表数据
func (s *ShiftServiceImpl) buildReportFromRealtime(ctx context.Context, shift repository.Shift, options repository.ShiftReportOptions) (repository.ShiftReportDTO, error) {
	// 构建班次基本信息
	shiftInfo := s.buildShiftInfo(ctx, shift, options)

	// TODO: 实现实时计算逻辑
	// 这里需要查询orders, order_items, order_payments, fuel_transactions等表
	// 进行实时聚合计算

	// 暂时返回空数据结构，避免编译错误
	return repository.ShiftReportDTO{
		ShiftInfo:          shiftInfo,
		PaymentSummary:     repository.PaymentSummaryDTO{},
		FuelSummary:        repository.FuelSummaryDTO{},
		MerchandiseSummary: repository.MerchandiseSummaryDTO{},
		TeraSummary:        repository.TeraSummaryDTO{},
		ReceiptInfo:        s.buildReceiptInfo(shift, options),
	}, nil
}

// buildShiftInfo 构建班次基本信息
func (s *ShiftServiceImpl) buildShiftInfo(ctx context.Context, shift repository.Shift, options repository.ShiftReportOptions) repository.ShiftInfoDTO {
	var durationHours *float64
	if shift.EndTime != nil {
		duration := shift.EndTime.Sub(shift.StartTime).Hours()
		durationHours = &duration
	}

	status := "active"
	if shift.EndTime != nil {
		status = "completed"
	}

	// 获取真实的站点名称
	stationName := fmt.Sprintf("Station %d", shift.StationID) // 默认值
	if s.stationService != nil {
		if realStationName, err := s.stationService.GetStationName(ctx, shift.StationID); err == nil {
			stationName = realStationName
		}
		// 如果获取失败，使用默认值，不中断流程
	}

	return repository.ShiftInfoDTO{
		ID:            shift.ID,
		ShiftNumber:   shift.ShiftNumber,
		StationID:     shift.StationID,
		StationName:   stationName,
		StaffID:       nil,                                             // TODO: 从班次中获取员工ID，当前Shift结构中没有此字段
		StaffName:     nil,                                             // TODO: 从员工服务获取
		StartTime:     shift.StartTime,
		EndTime:       shift.EndTime,
		DurationHours: durationHours,
		Status:        status,
		CreatedAt:     shift.CreatedAt,
		UpdatedAt:     shift.UpdatedAt,
	}
}

// buildReceiptInfo 构建小票信息
func (s *ShiftServiceImpl) buildReceiptInfo(shift repository.Shift, options repository.ShiftReportOptions) repository.ReceiptInfoDTO {
	now := time.Now().In(utils.JakartaLocation)

	// 设置默认值
	currency := "IDR"
	if options.Currency != "" {
		currency = options.Currency
	}

	timezone := "Asia/Jakarta"
	if options.Timezone != "" {
		timezone = options.Timezone
	}

	return repository.ReceiptInfoDTO{
		PrintTime:     now,
		ReceiptNumber: fmt.Sprintf("SR-%s-%d", shift.ShiftNumber, now.Unix()),
		Currency:      currency,
		Timezone:      timezone,
	}
}

// convertToReceiptFormat 转换为小票格式
func (s *ShiftServiceImpl) convertToReceiptFormat(reportData repository.ShiftReportDTO, options repository.ShiftReportOptions) (repository.ShiftReceiptDTO, error) {
	// 构建小票头部
	header := repository.ReceiptHeaderDTO{
		StationName: reportData.ShiftInfo.StationName,
		Address:     "Station Address", // TODO: 从配置获取
		Phone:       "Station Phone",   // TODO: 从配置获取
	}

	// 构建班次信息
	shiftInfo := repository.ReceiptShiftDTO{
		ShiftNumber: reportData.ShiftInfo.ShiftNumber,
		StaffName:   "Staff Name", // TODO: 从员工信息获取
		StartTime:   reportData.ShiftInfo.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:     "",
	}
	if reportData.ShiftInfo.EndTime != nil {
		shiftInfo.EndTime = reportData.ShiftInfo.EndTime.Format("2006-01-02 15:04:05")
	}

	// 构建销售汇总文本
	salesSummary := s.buildSalesSummaryText(reportData, options)

	// 构建底部信息
	footer := repository.ReceiptFooterDTO{
		PrintTime:       reportData.ReceiptInfo.PrintTime.Format("2006-01-02 15:04:05"),
		ReceiptNumber:   reportData.ReceiptInfo.ReceiptNumber,
		ThankYouMessage: "Thank you for your service!",
	}

	return repository.ShiftReceiptDTO{
		Header:       header,
		ShiftInfo:    shiftInfo,
		SalesSummary: salesSummary,
		Footer:       footer,
	}, nil
}

// buildSalesSummaryText 构建销售汇总文本
func (s *ShiftServiceImpl) buildSalesSummaryText(reportData repository.ShiftReportDTO, options repository.ShiftReportOptions) []string {
	var lines []string

	// 添加标题
	lines = append(lines, "=================================")
	lines = append(lines, "         SALES SUMMARY          ")
	lines = append(lines, "=================================")

	// 添加支付方式汇总
	for _, payment := range reportData.PaymentSummary.PaymentMethods {
		line := fmt.Sprintf("%-18s : %s %.0f", payment.MethodName, getCurrencySymbol(options.Currency), payment.Amount)
		lines = append(lines, line)
	}

	lines = append(lines, "---------------------------------")
	totalLine := fmt.Sprintf("%-18s : %s %.0f", "Total Sales", getCurrencySymbol(options.Currency), reportData.PaymentSummary.TotalSales)
	lines = append(lines, totalLine)

	// 添加油品信息
	lines = append(lines, "")
	lines = append(lines, "=================================")
	lines = append(lines, "         FUEL SUMMARY           ")
	lines = append(lines, "=================================")

	for _, fuel := range reportData.FuelSummary.FuelGrades {
		line := fmt.Sprintf("%-18s : %.1f L", fuel.Name, fuel.Volume)
		lines = append(lines, line)
	}

	// 添加非油品信息
	lines = append(lines, "")
	lines = append(lines, "=================================")
	lines = append(lines, "      MERCHANDISE SUMMARY       ")
	lines = append(lines, "=================================")

	itemsLine := fmt.Sprintf("%-18s : %d", "Total Items", reportData.MerchandiseSummary.TotalQuantity)
	lines = append(lines, itemsLine)

	merchandiseLine := fmt.Sprintf("%-18s : %s %.0f", "Merchandise Sales", getCurrencySymbol(options.Currency), reportData.MerchandiseSummary.TotalNetSales)
	lines = append(lines, merchandiseLine)

	// 添加TERA汇总
	lines = append(lines, "")
	lines = append(lines, "=================================")
	lines = append(lines, "        TERA SUMMARY            ")
	lines = append(lines, "=================================")

	lines = append(lines, "FUEL CATEGORY:")
	fuelGrossLine := fmt.Sprintf("  %-16s : %s %.0f", "Gross Sales", getCurrencySymbol(options.Currency), reportData.TeraSummary.Fuel.GrossSales)
	lines = append(lines, fuelGrossLine)

	fuelDiscountLine := fmt.Sprintf("  %-16s : %s %.0f", "Total Discount", getCurrencySymbol(options.Currency), reportData.TeraSummary.Fuel.TotalDiscount)
	lines = append(lines, fuelDiscountLine)

	fuelNetLine := fmt.Sprintf("  %-16s : %s %.0f", "Net Sales", getCurrencySymbol(options.Currency), reportData.TeraSummary.Fuel.NetSales)
	lines = append(lines, fuelNetLine)

	lines = append(lines, "")
	lines = append(lines, "MERCHANDISE CATEGORY:")
	merchGrossLine := fmt.Sprintf("  %-16s : %s %.0f", "Gross Sales", getCurrencySymbol(options.Currency), reportData.TeraSummary.Merchandise.GrossSales)
	lines = append(lines, merchGrossLine)

	merchDiscountLine := fmt.Sprintf("  %-16s : %s %.0f", "Total Discount", getCurrencySymbol(options.Currency), reportData.TeraSummary.Merchandise.TotalDiscount)
	lines = append(lines, merchDiscountLine)

	merchNetLine := fmt.Sprintf("  %-16s : %s %.0f", "Net Sales", getCurrencySymbol(options.Currency), reportData.TeraSummary.Merchandise.NetSales)
	lines = append(lines, merchNetLine)

	// 添加总计
	lines = append(lines, "")
	lines = append(lines, "=================================")
	lines = append(lines, "         GRAND TOTAL            ")
	lines = append(lines, "=================================")

	grandGrossLine := fmt.Sprintf("%-18s : %s %.0f", "Gross Sales", getCurrencySymbol(options.Currency), reportData.TeraSummary.Total.GrossSales)
	lines = append(lines, grandGrossLine)

	grandDiscountLine := fmt.Sprintf("%-18s : %s %.0f", "Total Discount", getCurrencySymbol(options.Currency), reportData.TeraSummary.Total.TotalDiscount)
	lines = append(lines, grandDiscountLine)

	grandNetLine := fmt.Sprintf("%-18s : %s %.0f", "Net Sales", getCurrencySymbol(options.Currency), reportData.TeraSummary.Total.NetSales)
	lines = append(lines, grandNetLine)

	return lines
}

// getCurrencySymbol 获取货币符号
func getCurrencySymbol(currency string) string {
	switch currency {
	case "USD":
		return "$"
	case "IDR":
		return "Rp"
	default:
		return "Rp"
	}
}

// 以下是从汇总表构建各种数据的辅助方法
// 这些方法需要根据实际的汇总表结构来实现

// buildPaymentSummaryFromSummary 从汇总表构建支付方式汇总
func (s *ShiftServiceImpl) buildPaymentSummaryFromSummary(ctx context.Context, shiftID repository.ID, summary repository.ShiftSummaryDTO) repository.PaymentSummaryDTO {
	// 从汇总表获取支付方式明细数据
	paymentDetails, err := s.shiftSummaryRepo.GetPaymentDetails(ctx, shiftID)
	if err != nil {
		// 如果获取失败，使用汇总数据中的基本信息
		return repository.PaymentSummaryDTO{
			TotalSales:        summary.TotalSales,
			TotalTransactions: 0,
			PaymentMethods:    []repository.PaymentMethodDetail{},
		}
	}

	var paymentMethods []repository.PaymentMethodDetail
	var totalTransactions int

	// 转换支付方式明细数据
	for _, detail := range paymentDetails {
		methodName := s.getPaymentMethodName(detail.PaymentMethod)

		// 计算百分比
		percentage := 0.0
		if summary.TotalSales > 0 {
			percentage = (detail.TotalAmount / summary.TotalSales) * 100
		}

		paymentMethods = append(paymentMethods, repository.PaymentMethodDetail{
			Method:           detail.PaymentMethod,
			MethodName:       methodName,
			Amount:           detail.TotalAmount,
			TransactionCount: detail.TransactionCount,
			Percentage:       percentage,
		})

		totalTransactions += detail.TransactionCount
	}

	return repository.PaymentSummaryDTO{
		TotalSales:        summary.TotalSales,
		TotalTransactions: totalTransactions,
		PaymentMethods:    paymentMethods,
	}
}

// buildFuelSummaryFromSummary 从汇总表构建油品汇总
func (s *ShiftServiceImpl) buildFuelSummaryFromSummary(ctx context.Context, shiftID repository.ID, summary repository.ShiftSummaryDTO) repository.FuelSummaryDTO {
	// 从汇总表获取油品明细数据
	fuelDetails, err := s.shiftSummaryRepo.GetFuelDetails(ctx, shiftID)
	if err != nil {
		// 如果获取失败，使用汇总数据中的基本信息
		return repository.FuelSummaryDTO{
			TotalVolume:       summary.TotalFuelVolume,
			TotalGrossSales:   summary.TotalFuelSales + summary.TotalFuelDiscount,
			TotalDiscount:     summary.TotalFuelDiscount,
			TotalNetSales:     summary.NetFuelSales,
			TotalTransactions: 0,
			FuelGrades:        []repository.FuelGradeDetail{},
		}
	}

	var fuelGrades []repository.FuelGradeDetail
	var totalTransactions int
	totalGrossSales := 0.0
	totalDiscount := 0.0
	totalNetSales := 0.0
	totalVolume := 0.0

	// 转换油品明细数据
	for _, detail := range fuelDetails {
		// 计算体积百分比
		volumePercentage := 0.0
		if summary.TotalFuelVolume > 0 {
			volumePercentage = (detail.TotalVolume / summary.TotalFuelVolume) * 100
		}

		// 获取油品名称和类型
		fuelName := s.getFuelName(detail.FuelGrade)
		fuelType := s.getFuelType(detail.FuelGrade)

		fuelGrades = append(fuelGrades, repository.FuelGradeDetail{
			Grade:            detail.FuelGrade,
			Type:             fuelType,
			Name:             fuelName,
			Volume:           detail.TotalVolume,
			GrossAmount:      detail.GrossAmount,
			DiscountAmount:   detail.TotalDiscountAmount,
			NetAmount:        detail.NetAmount,
			AveragePrice:     detail.AveragePrice,
			TransactionCount: detail.TransactionCount,
			VolumePercentage: volumePercentage,
		})

		totalTransactions += detail.TransactionCount
		totalGrossSales += detail.GrossAmount
		totalDiscount += detail.TotalDiscountAmount
		totalNetSales += detail.NetAmount
		totalVolume += detail.TotalVolume
	}

	return repository.FuelSummaryDTO{
		TotalVolume:       totalVolume,
		TotalGrossSales:   totalGrossSales,
		TotalDiscount:     totalDiscount,
		TotalNetSales:     totalNetSales,
		TotalTransactions: totalTransactions,
		FuelGrades:        fuelGrades,
	}
}

// buildMerchandiseSummaryFromSummary 从汇总表构建非油品汇总
func (s *ShiftServiceImpl) buildMerchandiseSummaryFromSummary(ctx context.Context, shiftID repository.ID, summary repository.ShiftSummaryDTO) repository.MerchandiseSummaryDTO {
	// 从汇总表获取非油品明细数据
	merchandiseDetails, err := s.shiftSummaryRepo.GetMerchandiseDetails(ctx, shiftID)
	if err != nil {
		// 如果获取失败，使用汇总数据中的基本信息
		return repository.MerchandiseSummaryDTO{
			TotalQuantity:     summary.MerchandiseSummary.TotalQuantity,
			TotalGrossSales:   summary.MerchandiseSummary.TotalSales + summary.MerchandiseSummary.TotalDiscount,
			TotalDiscount:     summary.MerchandiseSummary.TotalDiscount,
			TotalNetSales:     summary.MerchandiseSummary.NetSales,
			TotalTransactions: 0,
			TopProducts:       []repository.MerchandiseProduct{},
		}
	}

	var topProducts []repository.MerchandiseProduct
	var totalTransactions int
	totalQuantity := 0
	totalGrossSales := 0.0
	totalDiscount := 0.0
	totalNetSales := 0.0

	// 转换非油品明细数据，按销量排序取前10个
	for _, detail := range merchandiseDetails {
		// 转换数量为整数
		quantity := int(detail.TotalQuantity)

		topProducts = append(topProducts, repository.MerchandiseProduct{
			ProductID:        detail.ProductID,
			ProductName:      detail.ProductName,
			ProductType:      detail.ProductType,
			Category:         s.getProductCategory(detail.ProductCategory),
			Quantity:         quantity,
			UnitPrice:        detail.UnitPrice,
			GrossAmount:      detail.TotalAmount + detail.TotalDiscountAmount,
			DiscountAmount:   detail.TotalDiscountAmount,
			NetAmount:        detail.NetAmount,
			TransactionCount: detail.TransactionCount,
		})

		totalTransactions += detail.TransactionCount
		totalQuantity += quantity
		totalGrossSales += (detail.TotalAmount + detail.TotalDiscountAmount)
		totalDiscount += detail.TotalDiscountAmount
		totalNetSales += detail.NetAmount
	}

	// 按销量排序，取前10个
	if len(topProducts) > 10 {
		// 简单排序逻辑，按净销售额排序
		for i := 0; i < len(topProducts)-1; i++ {
			for j := i + 1; j < len(topProducts); j++ {
				if topProducts[i].NetAmount < topProducts[j].NetAmount {
					topProducts[i], topProducts[j] = topProducts[j], topProducts[i]
				}
			}
		}
		topProducts = topProducts[:10]
	}

	return repository.MerchandiseSummaryDTO{
		TotalQuantity:     totalQuantity,
		TotalGrossSales:   totalGrossSales,
		TotalDiscount:     totalDiscount,
		TotalNetSales:     totalNetSales,
		TotalTransactions: totalTransactions,
		TopProducts:       topProducts,
	}
}

// buildTeraSummaryFromSummary 从汇总表构建TERA分类汇总
func (s *ShiftServiceImpl) buildTeraSummaryFromSummary(ctx context.Context, shiftID repository.ID, summary repository.ShiftSummaryDTO) repository.TeraSummaryDTO {
	// 计算油品汇总
	fuelGrossSales := summary.TotalFuelSales + summary.TotalFuelDiscount
	fuelNetSales := summary.NetFuelSales

	// 计算非油品汇总
	merchandiseGrossSales := summary.MerchandiseSummary.TotalSales + summary.MerchandiseSummary.TotalDiscount
	merchandiseNetSales := summary.MerchandiseSummary.NetSales

	// 计算总计
	totalGrossSales := fuelGrossSales + merchandiseGrossSales
	totalDiscount := summary.TotalFuelDiscount + summary.MerchandiseSummary.TotalDiscount
	totalNetSales := fuelNetSales + merchandiseNetSales

	// 计算百分比
	fuelPercentage := 0.0
	merchandisePercentage := 0.0
	if totalNetSales > 0 {
		fuelPercentage = (fuelNetSales / totalNetSales) * 100
		merchandisePercentage = (merchandiseNetSales / totalNetSales) * 100
	}

	return repository.TeraSummaryDTO{
		Fuel: repository.TeraCategoryDTO{
			GrossSales:    fuelGrossSales,
			TotalDiscount: summary.TotalFuelDiscount,
			NetSales:      fuelNetSales,
			Percentage:    fuelPercentage,
		},
		Merchandise: repository.TeraCategoryDTO{
			GrossSales:    merchandiseGrossSales,
			TotalDiscount: summary.MerchandiseSummary.TotalDiscount,
			NetSales:      merchandiseNetSales,
			Percentage:    merchandisePercentage,
		},
		Total: repository.TeraTotalDTO{
			GrossSales:    totalGrossSales,
			TotalDiscount: totalDiscount,
			NetSales:      totalNetSales,
		},
	}
}

// 辅助方法：获取支付方式名称
func (s *ShiftServiceImpl) getPaymentMethodName(method string) string {
	methodNames := map[string]string{
		"cash":        "Cash",
		"credit_card": "Credit Card",
		"debit_card":  "Debit Card",
		"e_wallet":    "E-Wallet",
		"voucher":     "Voucher",
		"fleet_card":  "Fleet Card",
	}

	if name, exists := methodNames[method]; exists {
		return name
	}
	return method
}

// 辅助方法：获取油品名称
func (s *ShiftServiceImpl) getFuelName(grade string) string {
	fuelNames := map[string]string{
		"92":     "Pertamax 92",
		"95":     "Pertamax 95",
		"diesel": "Solar",
		"ron92":  "RON 92",
		"ron95":  "RON 95",
	}

	if name, exists := fuelNames[grade]; exists {
		return name
	}
	return grade
}

// 辅助方法：获取油品类型
func (s *ShiftServiceImpl) getFuelType(grade string) string {
	if grade == "diesel" {
		return "diesel"
	}
	return "gasoline"
}

// 辅助方法：获取商品分类
func (s *ShiftServiceImpl) getProductCategory(category *string) string {
	if category == nil {
		return "other"
	}
	return *category
}
