package handlers

import (
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"
	"sync"
	"bytes"
	"encoding/json"
	"io"


	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/models"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"

	// 支付服务相关导入
	paymentModel "gitlab4.weicheche.cn/indo-bp/payment-service/pkg/payment/model"
	paymentService "gitlab4.weicheche.cn/indo-bp/payment-service/pkg/payment/service"
)

// 全局变量用于存储燃油交易锁
var fuelTransactionLocks sync.Map

// formatPaymentMethodID 将支付方式ID格式化为字符串
func formatPaymentMethodID(paymentMethodID int64) string {
	return fmt.Sprintf("%d", paymentMethodID)
}

// parsePaymentMethodID 将字符串解析为支付方式ID
func parsePaymentMethodID(paymentMethodStr string) (int64, error) {
	return strconv.ParseInt(paymentMethodStr, 10, 64)
}

// OrderHandler 处理与订单相关的请求
type OrderHandler struct {
	orderService           service.OrderService
	fuelTransactionService service.FuelTransactionService
	paymentService         paymentService.PaymentService
	invoiceGenerator       utils.InvoiceNumberGenerator
}

// NewOrderHandler 创建新的订单处理器
func NewOrderHandler(orderService service.OrderService, fuelTransactionService service.FuelTransactionService, paymentService paymentService.PaymentService) *OrderHandler {
	// 创建站点查询函数（这里需要根据实际的数据库连接来调整）
	// 暂时使用模拟函数，实际使用时需要传入真实的数据库连接
	stationQueryFunc := utils.CreateMockStationQueryFunc()

	// 创建订单号生成器
	invoiceGenerator := utils.NewRedisInvoiceGenerator(stationQueryFunc)

	return &OrderHandler{
		orderService:           orderService,
		fuelTransactionService: fuelTransactionService,
		paymentService:         paymentService,
		invoiceGenerator:       invoiceGenerator,
	}
}

// NewOrderHandlerWithDB 创建新的订单处理器（带数据库连接）
func NewOrderHandlerWithDB(orderService service.OrderService, fuelTransactionService service.FuelTransactionService, paymentService paymentService.PaymentService, db interface{}) *OrderHandler {
	// 创建真实的站点查询函数
	stationQueryFunc := utils.CreateStationQueryFunc(db)

	// 创建订单号生成器
	invoiceGenerator := utils.NewRedisInvoiceGenerator(stationQueryFunc)

	return &OrderHandler{
		orderService:           orderService,
		fuelTransactionService: fuelTransactionService,
		paymentService:         paymentService,
		invoiceGenerator:       invoiceGenerator,
	}
}

// ListOrdersResponse 定义列表响应结构
type ListOrdersResponse struct {
	Items     []repository.Order `json:"items"`
	Total     int                `json:"total"`
	Page      int                `json:"page"`
	PageSize  int                `json:"page_size"`
	TotalPage int                `json:"total_page"`
}

// ListOrders 处理获取订单列表的请求
// @Summary 列出订单
// @Description 获取订单列表，支持分页和过滤
// @Tags 订单
// @Accept json
// @Produce json
// @Param customer_id query int false "客户ID"
// @Param station_id query int false "加油站ID"
// @Param status query string false "订单状态 (new, processing, completed, cancelled)"
// @Param date_from query string false "开始日期 (格式: 2006-01-02)"
// @Param date_to query string false "结束日期 (格式: 2006-01-02)"
// @Param order_number query string false "订单编号"
// @Param product_type query string false "产品类型"
// @Param payment_method query string false "支付方式"
// @Param employee_no query string false "员工编号"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Param sort_by query string false "排序字段，默认为created_at"
// @Param sort_dir query string false "排序方向 (asc, desc)，默认为desc"
// @Success 200 {object} ListOrdersResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders [get]
func (h *OrderHandler) ListOrders(c echo.Context) error {
	ctx := context.Background()

	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit <= 0 {
		limit = 10
	}

	pagination := repository.Pagination{
		Page:  page,
		Limit: limit,
	}

	// 解析排序参数
	sortField := c.QueryParam("sort_by")
	if sortField == "" {
		sortField = "created_at"
	}

	sortDir := c.QueryParam("sort_dir")
	if sortDir != "asc" {
		sortDir = "desc" // 默认降序
	}

	sortOrder := repository.SortOrder{
		Field:     sortField,
		Direction: sortDir,
	}

	// 构建过滤条件
	filter := repository.OrderFilter{}

	// 解析并设置过滤参数
	if c.QueryParam("customer_id") != "" {
		customerID, err := strconv.ParseInt(c.QueryParam("customer_id"), 10, 64)
		if err == nil && customerID > 0 {
			id := int64ToUUID(customerID)
			filter.CustomerID = &id
		}
	}

	if c.QueryParam("station_id") != "" {
		stationID, err := strconv.ParseInt(c.QueryParam("station_id"), 10, 64)
		if err == nil && stationID > 0 {
			id := int64ToUUID(stationID)
			filter.StationID = &id
		}
	}

	if c.QueryParam("status") != "" {
		status := repository.OrderStatus(c.QueryParam("status"))
		filter.Status = &status
	}

	// 解析日期参数
	if c.QueryParam("date_from") != "" {
		dateFrom, err := time.Parse("2006-01-02", c.QueryParam("date_from"))
		if err == nil {
			filter.DateFrom = &dateFrom
		}
	}

	if c.QueryParam("date_to") != "" {
		dateTo, err := time.Parse("2006-01-02", c.QueryParam("date_to"))
		if err == nil {
			// 设置为当天的结束时间
			dateTo = time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), 23, 59, 59, 999999999, dateTo.Location())
			filter.DateTo = &dateTo
		}
	}

	if c.QueryParam("order_number") != "" {
		orderNumber := c.QueryParam("order_number")
		filter.OrderNumber = &orderNumber
	}

	if c.QueryParam("product_type") != "" {
		productType := c.QueryParam("product_type")
		filter.ProductType = &productType
	}

	if c.QueryParam("payment_method") != "" {
		paymentMethod := c.QueryParam("payment_method")
		filter.PaymentMethod = &paymentMethod
	}

	if c.QueryParam("employee_no") != "" {
		employeeNo := c.QueryParam("employee_no")
		filter.EmployeeNo = &employeeNo
	}

	// 调用服务获取数据
	orders, total, err := h.orderService.ListOrders(ctx, filter, pagination, sortOrder)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch orders",
		))
	}

	// 计算总页数
	totalPage := total / limit
	if total%limit > 0 {
		totalPage++
	}

	// 构建响应数据
	responseData := ListOrdersResponse{
		Items:     orders,
		Total:     total,
		Page:      page,
		PageSize:  limit,
		TotalPage: totalPage,
	}

	// 直接返回数据，不使用统一格式封装
	return c.JSON(http.StatusOK, responseData)
}

// CreateOrderRequest 定义创建订单的请求结构
type CreateOrderRequest struct {
	FuelTransactionID int64                  `json:"fuel_transaction_id" validate:"required"`
	StationID         int64                  `json:"station_id" validate:"required"`
	CustomerID        *int64                 `json:"customer_id,omitempty"`
	CustomerName      *string                `json:"customer_name,omitempty"`
	CustomerPhone     *string                `json:"customer_phone,omitempty"`     // 客户手机号
	VehicleType       *string                `json:"vehicle_type,omitempty"`       // 车型（Car, Motorbike, Truck等）
	LicensePlate      *string                `json:"license_plate,omitempty"`      // 车牌号
	EmployeeNo        *string                `json:"employee_no,omitempty"`
	TerminalID        *string                `json:"terminal_id,omitempty"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
	PaymentType       string                 `json:"payment_type" validate:"required"`   // 支付类型 (CASH, BANK_CARD, WECHAT, ALIPAY, POINTS, COUPON, MIXED)
	PaymentMethod     int64                  `json:"payment_method" validate:"required"` // 具体支付方式ID
	AllocatedAmount   *float64               `json:"allocated_amount,omitempty"`
	ReceivedAmount    *float64               `json:"received_amount,omitempty"`    // 现金支付时的收款金额
}

// CreateOrder 处理创建订单的请求
// @Summary 创建订单
// @Description 创建新的订单记录
// @Tags 订单
// @Accept json
// @Produce json
// @Param order body CreateOrderRequest true "订单信息"
// @Success 201 {object} repository.Order
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders [post]
func (h *OrderHandler) CreateOrder(c echo.Context) error {
	ctx := context.Background()
	var req CreateOrderRequest

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid request parameters",
		))
	}

	// 条件验证 - 只有在验证器存在时才进行验证
	if c.Echo().Validator != nil {
		if err := c.Validate(&req); err != nil {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeValidationError,
				"Request parameter validation failed",
			))
		}
	}

	// 🔧 新增：使用燃油交易ID作为锁key，防止并发创建重复订单
	fuelTransactionID := int64ToUUID(req.FuelTransactionID)
	lockKey := fmt.Sprintf("fuel_transaction_%d", fuelTransactionID)
	
	// 获取或创建锁
	lockInterface, _ := fuelTransactionLocks.LoadOrStore(lockKey, &sync.Mutex{})
	lock := lockInterface.(*sync.Mutex)
	
	// 加锁
	lock.Lock()
	defer func() {
		lock.Unlock()
		// 清理锁，避免内存泄漏
		fuelTransactionLocks.Delete(lockKey)
	}()
	
	log.Printf("🔒 获取燃油交易锁 - ID: %d, 开始处理订单创建", fuelTransactionID)

	// 1. 根据fuel_transaction_id查询燃油交易
	fuelTransaction, err := h.fuelTransactionService.GetFuelTransaction(ctx, fuelTransactionID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to query fuel transaction",
		))
	}

	// 添加详细的调试日志
	log.Printf("燃油交易查询结果 - ID: %d, Amount: %.6f", fuelTransaction.ID, fuelTransaction.Amount)
	if req.AllocatedAmount != nil {
		log.Printf("请求中的分配金额: %.6f", *req.AllocatedAmount)
		log.Printf("金额差值: %.6f", math.Abs(*req.AllocatedAmount-fuelTransaction.Amount))
	}

	// 当前只支持燃油交易关联单笔订单，不支持关联多笔订单。这里检查订单金额是否等于燃油交易金额（注意浮点数比较精度问题）
	epsilon := 0.000001
	
	// 确定当前要分配的金额，并处理货币单位转换
	var allocatedAmount float64
	if req.AllocatedAmount != nil {
		// 处理货币单位不匹配问题：
		// 如果请求金额比燃油交易金额大1000倍左右，说明请求使用的是最小货币单位（如印尼盾分）
		// 而数据库存储的是主要货币单位（如印尼盾元）
		allocatedAmount = *req.AllocatedAmount
		fuelAmount := fuelTransaction.Amount
		
		// 检查是否需要单位转换（容忍10%的误差来判断是否为1000倍关系）
		ratio := allocatedAmount / fuelAmount
		if ratio > 900 && ratio < 1100 { // 约1000倍，允许10%误差
			// 将请求金额转换为主要单位（除以1000）
			allocatedAmount = allocatedAmount / 1000.0
			log.Printf("检测到货币单位不匹配，将请求金额从 %.2f 转换为 %.6f", *req.AllocatedAmount, allocatedAmount)
		}
		
		// 进行金额比较
		amountDiff := math.Abs(allocatedAmount - fuelAmount)
		if amountDiff > epsilon {
			log.Printf("金额验证失败 - 燃油交易金额: %.6f, 转换后分配金额: %.6f, 差值: %.6f, 阈值: %.6f",
				fuelAmount, allocatedAmount, amountDiff, epsilon)
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				fmt.Sprintf("Allocated amount must be equal to fuel transaction amount (fuel: %.2f, allocated: %.2f)", 
					fuelAmount, allocatedAmount),
			))
		}
		
		log.Printf("金额验证通过 - 燃油交易金额: %.6f, 转换后分配金额: %.6f", fuelAmount, allocatedAmount)
	} else {
		// 如果没有指定分配金额，使用燃油交易的全部金额
		allocatedAmount = fuelTransaction.Amount
	}

	// 使用GetActiveFuelTransactionLinks方法获取所有活跃的燃油交易链接，并计算已分配的总金额

	// 2. 检查燃油交易是否已经关联过订单且是否有足够的可用金额
	activeLinks, err := h.fuelTransactionService.GetActiveFuelTransactionLinks(ctx, fuelTransactionID)
	if err != nil {
		log.Printf("❌ 查询燃油交易活跃链接失败 - ID: %d, 错误: %v", fuelTransactionID, err)
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to query fuel transaction links",
		))
	}

	// 计算已分配的金额 - 累加所有活跃链接的AllocatedAmount
	var allocatedTotal float64 = 0
	for _, link := range activeLinks {
		allocatedTotal += link.AllocatedAmount
		log.Printf("📊 现有活跃链接 - LinkID: %d, OrderID: %d, 分配金额: %.2f", 
			link.ID, link.OrderID, link.AllocatedAmount)
	}
	
	log.Printf("📈 燃油交易金额统计 - ID: %d, 总金额: %.2f, 已分配: %.2f, 本次分配: %.2f", 
		fuelTransactionID, fuelTransaction.Amount, allocatedTotal, allocatedAmount)

	// 🔧 优先检查是否为重复分配（如果已分配金额等于总金额，说明已经被完全分配了）
	// 在金额不足检查之前进行，以便执行自动清理逻辑
	const duplicateEpsilon = 0.01
	if math.Abs(allocatedTotal - fuelTransaction.Amount) <= duplicateEpsilon {
		log.Printf("⚠️ 燃油交易已被完全分配 - 总金额: %.2f, 已分配: %.2f, 活跃链接数: %d", 
			fuelTransaction.Amount, allocatedTotal, len(activeLinks))
		
		// 检查现有的活跃链接对应的订单状态
		var shouldCleanup bool = false
		var problematicOrders []repository.ID
		
		for _, link := range activeLinks {
			order, err := h.orderService.GetOrder(ctx, link.OrderID)
			if err != nil {
				log.Printf("❌ 获取订单 %d 失败: %v", link.OrderID, err)
				shouldCleanup = true
				problematicOrders = append(problematicOrders, link.OrderID)
				continue
			}
			
			// 检查订单状态 - 如果订单不是已完成状态，说明可能存在问题需要清理
			if order.Status == repository.OrderStatusNew || 
			   order.Status == repository.OrderStatusProcessing || 
			   order.Status == repository.OrderStatusCancelled {
				log.Printf("⚠️ 发现问题订单 - OrderID: %d, Status: %s, 需要清理", order.ID, order.Status)
				shouldCleanup = true
				problematicOrders = append(problematicOrders, order.ID)
			}
		}
		
		if shouldCleanup {
			log.Printf("🔧 开始清理问题订单和关联，以便重新创建订单")
			
			// 逐一清理问题订单和关联
			for _, problemOrderID := range problematicOrders {
				// 取消订单（这会自动解除燃油交易关联）
				log.Printf("🗑️ 取消问题订单 - OrderID: %d", problemOrderID)
				_, cancelErr := h.orderService.CancelOrder(ctx, problemOrderID)
				if cancelErr != nil {
					log.Printf("❌ 取消订单 %d 失败: %v", problemOrderID, cancelErr)
					// 如果取消订单失败，尝试直接解除关联
					unlinkErr := h.fuelTransactionService.UnlinkFuelTransactionFromOrder(ctx, fuelTransactionID, problemOrderID)
					if unlinkErr != nil {
						log.Printf("❌ 直接解除关联也失败 - 燃油交易ID: %d, 订单ID: %d, 错误: %v", 
							fuelTransactionID, problemOrderID, unlinkErr)
					} else {
						log.Printf("✅ 直接解除关联成功 - 燃油交易ID: %d, 订单ID: %d", fuelTransactionID, problemOrderID)
					}
				} else {
					log.Printf("✅ 成功取消问题订单 - OrderID: %d", problemOrderID)
				}
			}
			
			// 重新检查活跃链接
			log.Printf("🔍 重新检查清理后的活跃链接")
			activeLinks, err = h.fuelTransactionService.GetActiveFuelTransactionLinks(ctx, fuelTransactionID)
			if err != nil {
				log.Printf("❌ 重新查询燃油交易活跃链接失败 - ID: %d, 错误: %v", fuelTransactionID, err)
				return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
					models.CodeInternalServerError,
					"Failed to requery fuel transaction links after cleanup",
				))
			}
			
			// 重新计算已分配金额
			allocatedTotal = 0
			for _, link := range activeLinks {
				allocatedTotal += link.AllocatedAmount
				log.Printf("📊 清理后剩余活跃链接 - LinkID: %d, OrderID: %d, 分配金额: %.2f", 
					link.ID, link.OrderID, link.AllocatedAmount)
			}
			
			log.Printf("📈 清理后燃油交易金额统计 - ID: %d, 总金额: %.2f, 已分配: %.2f, 本次分配: %.2f", 
				fuelTransactionID, fuelTransaction.Amount, allocatedTotal, allocatedAmount)
			
			// 检查清理后是否还有冲突
			if math.Abs(allocatedTotal - fuelTransaction.Amount) <= duplicateEpsilon {
				log.Printf("❌ 清理后仍然冲突，无法创建新订单")
				return c.JSON(http.StatusConflict, models.NewErrorResponse(
					models.CodeConflict,
					"Fuel transaction still fully allocated after cleanup, cannot create new order",
				))
			}
			
			log.Printf("✅ 清理完成，可以继续创建新订单")
			
		} else {
			// 如果不需要清理，直接返回错误
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Fuel transaction has been fully allocated",
			))
		}
	}

	// 执行最终的金额检查（在自动清理之后）
	// 检查剩余可用金额是否足够
	// 修复：使用小于等于判断，当总金额等于已分配+本次分配时应该允许
	if fuelTransaction.Amount < allocatedTotal+allocatedAmount-epsilon {
		log.Printf("❌ 燃油交易金额不足 - 总金额: %.2f, 已分配: %.2f, 本次分配: %.2f", 
			fuelTransaction.Amount, allocatedTotal, allocatedAmount)
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Insufficient fuel transaction amount",
		))
	}

	// 使用新的订单号生成器
	// 格式：INV/{SITE_CODE}/{DAY_CODE}{RANDOM_NUMBERS}
	orderNumber, err := h.invoiceGenerator.GenerateInvoiceNumber(ctx, req.StationID)
	if err != nil {
		log.Printf("❌ 生成订单号失败: %v", err)
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to generate order number",
		))
	}
	log.Printf("✅ 生成的订单号: %s (长度: %d)", orderNumber, len(orderNumber))

	// 3. 创建订单（先进行促销计算，然后用正确金额创建订单）
	order := repository.Order{
		OrderNumber: orderNumber,
		StationID:   int64ToUUID(req.StationID),
		Metadata:    req.Metadata,
	}

	if req.CustomerID != nil {
		customerID := int64ToUUID(*req.CustomerID)
		order.CustomerID = &customerID
	}

	if req.CustomerName != nil {
		order.CustomerName = req.CustomerName
	}

	if req.CustomerPhone != nil {
		order.CustomerPhone = req.CustomerPhone
	}

	if req.VehicleType != nil {
		order.VehicleType = req.VehicleType
	}

	if req.LicensePlate != nil {
		order.LicensePlate = req.LicensePlate
	}

	if req.EmployeeNo != nil {
		order.EmployeeNo = req.EmployeeNo
	}

	// 调试日志：检查新字段是否正确设置
	customerName := ""
	if order.CustomerName != nil {
		customerName = *order.CustomerName
	}
	customerPhone := ""
	if order.CustomerPhone != nil {
		customerPhone = *order.CustomerPhone
	}
	vehicleType := ""
	if order.VehicleType != nil {
		vehicleType = *order.VehicleType
	}
	licensePlate := ""
	if order.LicensePlate != nil {
		licensePlate = *order.LicensePlate
	}
	log.Printf("🔍 订单字段调试 - CustomerName: '%s', CustomerPhone: '%s', VehicleType: '%s', LicensePlate: '%s'",
		customerName, customerPhone, vehicleType, licensePlate)

	// 4. 检查是否为tera支付方式，如果是则跳过促销计算
	isTeraPayment := h.isTeraPayment(req.PaymentType)
	log.Printf("🎯 支付方式检查 - PaymentType: %s, IsTeraPayment: %t", req.PaymentType, isTeraPayment)

	var finalAmount, discountAmount float64
	var appliedPromotions []AppliedPromotion
	var promotionCalculationSuccess bool = false

	if isTeraPayment {
		// tera支付跳过促销计算，直接使用原始金额
		log.Printf("🎯 检测到tera支付方式，跳过促销计算 - 使用原始金额: %.2f", allocatedAmount)
		finalAmount = allocatedAmount
		discountAmount = 0
		appliedPromotions = []AppliedPromotion{}
		promotionCalculationSuccess = false
	} else {
		// 非tera支付进行正常的促销计算处理
		log.Printf("🎯 开始促销计算处理 - 临时订单号: %s, 原始金额: %.2f", orderNumber, allocatedAmount)

		// 记录初始金额审计轨迹
		h.logAmountAuditTrail(orderNumber, "促销前", map[string]float64{
			"燃油交易金额": fuelTransaction.Amount,
			"分配金额":   allocatedAmount,
			"单价":     fuelTransaction.UnitPrice,
			"数量":     fuelTransaction.Volume,
		})

		// 4.1 构建促销计算请求
		promotionRequest := h.buildPromotionCalculatorRequest(orderNumber, &fuelTransaction, allocatedAmount, &req)

		// 4.2 调用促销计算API（带异常处理）
		promotionResponse, err := h.callPromotionCalculator(ctx, promotionRequest)
		if err != nil {
			// 促销计算失败，使用原始金额继续处理
			log.Printf("⚠️ 促销计算失败，使用原始金额: %v", err)
			h.logPromotionError("calculation", orderNumber, err, promotionRequest)

			fallbackResponse := h.createFallbackPromotionResponse(allocatedAmount)
			finalAmount = fallbackResponse.DiscountedAmount
			discountAmount = fallbackResponse.DiscountAmount
			appliedPromotions = fallbackResponse.AppliedPromotions
		} else {
			// 促销计算成功
			log.Printf("✅ 促销计算成功 - 原始金额: %.2f, 最终金额: %.2f, 优惠金额: %.2f",
				promotionResponse.OriginalAmount, promotionResponse.DiscountedAmount, promotionResponse.DiscountAmount)

			// 验证促销响应
			if err := h.validatePromotionResponse(promotionRequest, promotionResponse); err != nil {
				log.Printf("⚠️ 促销响应验证失败，使用原始金额: %v", err)
				fallbackResponse := h.createFallbackPromotionResponse(allocatedAmount)
				finalAmount = fallbackResponse.DiscountedAmount
				discountAmount = fallbackResponse.DiscountAmount
				appliedPromotions = fallbackResponse.AppliedPromotions
			} else {
				finalAmount = promotionResponse.DiscountedAmount
				discountAmount = promotionResponse.DiscountAmount
				appliedPromotions = promotionResponse.AppliedPromotions
				promotionCalculationSuccess = true
			}
		}

		// 4.3 验证金额一致性
		if err := h.validateAmountConsistency(allocatedAmount, finalAmount, discountAmount); err != nil {
			log.Printf("⚠️ 金额一致性验证失败: %v", err)
			// 重置为原始金额
			finalAmount = allocatedAmount
			discountAmount = 0
			appliedPromotions = []AppliedPromotion{}
		}
	}

	// 4.4 创建订单时不设置金额，让Repository在添加订单项时自动计算
	// 注意：OrderRepository的AddOrderItem会自动累加金额到订单上
	
	// 4.5 创建订单
	createdOrder, err := h.orderService.CreateOrder(ctx, order)
	if err != nil {
		// 添加详细的错误日志
		log.Printf("创建订单失败 - 详细错误: %v", err)
		log.Printf("订单数据: %+v", order)
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to create order",
		))
	}

	// 5. 创建订单项（使用促销后的金额）
	orderItemMetadata := map[string]interface{}{
		"promotion_applied": promotionCalculationSuccess,
		"original_amount":   allocatedAmount,
		"discount_amount":   discountAmount,
		"final_amount":      finalAmount,
	}
	
	// 添加促销详情到订单项metadata
	if promotionCalculationSuccess && len(appliedPromotions) > 0 {
		var promotionMetadata []map[string]interface{}
		for _, promotion := range appliedPromotions {
			promotionInfo := map[string]interface{}{
				"promotion_id":      promotion.PromotionID,
				"promotion_name":    promotion.PromotionName,
				"discount_type":     promotion.DiscountType,
				"discount_value":    promotion.DiscountValue,
				"discount_amount":   promotion.DiscountAmount,
				"description":       promotion.Description,
				"applicable_items":  promotion.ApplicableItems,
				"promotion_metadata": promotion.Metadata,
				"applied_at":        time.Now().Format("2006-01-02T15:04:05Z07:00"),
			}
			promotionMetadata = append(promotionMetadata, promotionInfo)
		}
		orderItemMetadata["applied_promotions"] = promotionMetadata
	}
	
	orderItem := repository.OrderItem{
		OrderID:        createdOrder.ID,
		ProductID:      h.getFuelTypeProductID(fuelTransaction.FuelType), // 修改：使用 fuel_type 转换为 product_id
		ProductName:    fuelTransaction.FuelGrade, // 修改：使用 fuel_grade 作为产品名称
		ProductType:    "fuel",
		Quantity:       fuelTransaction.Volume,
		UnitPrice:      fuelTransaction.UnitPrice,
		TotalPrice:     allocatedAmount,  // 原始总价
		DiscountAmount: discountAmount,   // 优惠金额
		FinalPrice:     allocatedAmount - discountAmount,  // 订单项的最终价格（原始价格减去折扣）
		FuelGrade:      &fuelTransaction.FuelGrade,
		PumpID:         &fuelTransaction.PumpID,
		Metadata:       orderItemMetadata,
	}

	_, err = h.orderService.AddOrderItem(ctx, createdOrder.ID, orderItem)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to add order item",
		))
	}

	// 5.1 获取添加订单项后的最新订单信息，检查金额是否正确
	updatedOrder, err := h.orderService.GetOrder(ctx, createdOrder.ID)
	if err != nil {
		log.Printf("⚠️ 获取订单信息失败: %v", err)
	} else {
		log.Printf("📊 订单金额检查 - OrderID: %d, TotalAmount: %.2f, FinalAmount: %.2f, DiscountAmount: %.2f", 
			updatedOrder.ID, updatedOrder.TotalAmount, updatedOrder.FinalAmount, updatedOrder.DiscountAmount)
		
		// 更新本地订单对象的金额信息，用于后续支付
		createdOrder.TotalAmount = updatedOrder.TotalAmount
		createdOrder.FinalAmount = updatedOrder.FinalAmount
		createdOrder.DiscountAmount = updatedOrder.DiscountAmount
		
		// 如果有促销折扣，记录信息
		if discountAmount > 0 {
			log.Printf("🎁 促销折扣将在支付时应用 - 计算的折扣: %.2f, 当前订单DiscountAmount: %.2f", 
				discountAmount, updatedOrder.DiscountAmount)
		}
	}

	// 6. 将促销信息写入order_promotions表（关键步骤）
	if promotionCalculationSuccess && len(appliedPromotions) > 0 {
		log.Printf("💾 开始写入促销信息到order_promotions表 - 促销数量: %d", len(appliedPromotions))
		
		for _, promotion := range appliedPromotions {
			// 直接使用原始的促销ID，不再转换为int64
			log.Printf("💾 保存促销信息 - 促销ID: %s (保持UUID格式)", promotion.PromotionID)

			// 构建促销记录
			orderPromotion := repository.OrderPromotion{
				PromotionID:    promotion.PromotionID, // 直接使用UUID字符串
				PromotionName:  promotion.PromotionName,
				PromotionType:  promotion.DiscountType,
				DiscountAmount: promotion.DiscountAmount,
				Metadata: map[string]interface{}{
					"discount_type":         promotion.DiscountType,
					"discount_value":        promotion.DiscountValue,
					"description":           promotion.Description,
					"applicable_items":      promotion.ApplicableItems,
					"promotion_metadata":    promotion.Metadata,
					"applied_at":           time.Now().Format("2006-01-02T15:04:05Z07:00"),
				},
			}

			// 写入order_promotions表
			appliedOrderPromotion, err := h.orderService.ApplyPromotion(ctx, createdOrder.ID, orderPromotion)
			if err != nil {
				log.Printf("⚠️ 写入促销记录失败 - PromotionID: %s, 错误: %v", promotion.PromotionID, err)
			} else {
				log.Printf("✅ 促销记录写入成功 - TableID: %d, PromotionID: %s, 优惠金额: %.2f", 
					appliedOrderPromotion.ID, promotion.PromotionID, promotion.DiscountAmount)
			}
		}
	}

	// 7. 关联订单和燃油交易
	_, err = h.fuelTransactionService.LinkFuelTransactionToOrder(ctx, fuelTransactionID, createdOrder.ID, allocatedAmount)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to link fuel transaction",
		))
	}

	// 7. 调用支付服务（使用促销后的最终金额）
	log.Printf("💰 开始处理订单 %s 的支付流程", createdOrder.OrderNumber)
	log.Printf("💰 支付金额详情 - 原始: %.2f, 优惠: %.2f, 最终: %.2f, 支付类型: %s", 
		allocatedAmount, discountAmount, finalAmount, req.PaymentType)

	// 最终金额验证
	if finalAmount <= 0 {
		log.Printf("❌ 最终支付金额无效: %.2f", finalAmount)
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			fmt.Sprintf("Invalid final payment amount: %.2f", finalAmount),
		))
	}

	// 检查支付服务是否可用
	if h.paymentService == nil {
		log.Printf("支付服务未初始化，跳过订单 %s 的支付处理\n", createdOrder.OrderNumber)
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Payment service unavailable",
		))
	}

	// 验证支付类型和支付方式
	if req.PaymentType == "" {
		log.Printf("支付类型为空，订单: %s", createdOrder.OrderNumber)
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Payment type is required",
		))
	}
	
	if req.PaymentMethod <= 0 {
		log.Printf("支付方式ID无效，订单: %s, PaymentMethod: %d", createdOrder.OrderNumber, req.PaymentMethod)
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid payment method",
		))
	}

	// 构建支付参数
	paymentParams := make(map[string]interface{})

	// 复制metadata到支付参数
	if req.Metadata != nil {
		for k, v := range req.Metadata {
			paymentParams[k] = v
		}
	}

	// 添加received_amount参数（现金支付必需）
	if req.ReceivedAmount != nil {
		paymentParams["received_amount"] = *req.ReceivedAmount
	}

	// 构建支付服务请求参数（使用最终金额）
	paymentReq := &paymentModel.PaymentRequest{
		OrderID:       createdOrder.OrderNumber,
		Amount:        finalAmount,  // 🔄 使用促销后的最终金额
		Currency:      "IDR", // 默认货币，可以从配置或请求中获取
		PaymentType:   paymentModel.PaymentType(req.PaymentType),
		PaymentMethod: req.PaymentMethod,
		PaymentParams: paymentParams,
		StationID:     req.StationID,
		Metadata: map[string]interface{}{
			"fuel_transaction_id": req.FuelTransactionID,
			"order_id":            createdOrder.ID,
			"original_amount":     allocatedAmount,
			"discount_amount":     discountAmount,
			"final_amount":        finalAmount,
			"promotion_applied":   promotionCalculationSuccess,
			"created_at":          time.Now(),
		},
	}

	// 如果有客户信息，添加到支付请求中
	if req.CustomerID != nil {
		paymentReq.CustomerID = fmt.Sprintf("%d", *req.CustomerID)
	}
	if req.CustomerName != nil {
		paymentReq.CustomerName = *req.CustomerName
	}
	if req.EmployeeNo != nil {
		paymentReq.OperatorID = *req.EmployeeNo
	}
	if req.TerminalID != nil {
		paymentReq.TerminalID = *req.TerminalID
	}

	// 调用支付服务处理支付
	paymentResp, err := h.paymentService.ProcessPayment(ctx, paymentReq)
	if err != nil || paymentResp.Status != paymentModel.PaymentStatusSuccess {
		// 支付处理失败，记录错误并可能需要更新订单状态
		log.Printf("订单 %s 支付处理失败: %v\n", createdOrder.OrderNumber, err)
		
		// 添加详细的支付请求和响应日志
		log.Printf("支付请求详情 - OrderID: %s, Amount: %.2f, PaymentType: %s, PaymentMethod: %d, StationID: %d",
			paymentReq.OrderID, paymentReq.Amount, paymentReq.PaymentType, paymentReq.PaymentMethod, paymentReq.StationID)
		
		if paymentResp != nil {
			log.Printf("支付响应详情 - Status: %s, PaymentID: %d", 
				paymentResp.Status, paymentResp.PaymentID)
			if paymentResp.PaymentNumber != "" {
				log.Printf("支付编号: %s", paymentResp.PaymentNumber)
			}
		} else {
			log.Printf("支付响应为空，可能是支付服务连接失败")
		}

		// 支付失败时，需要取消燃油交易关联
		log.Printf("支付失败，开始取消燃油交易关联 - 燃油交易ID: %d, 订单ID: %d", fuelTransactionID, createdOrder.ID)
		
		// 尝试取消燃油交易关联链接
		cancelErr := h.fuelTransactionService.CancelFuelTransactionLink(ctx, fuelTransactionID, createdOrder.ID)
		if cancelErr != nil {
			log.Printf("取消燃油交易关联失败: %v", cancelErr)
			// 注意：这里不中断流程，继续取消订单
		} else {
			log.Printf("成功取消燃油交易关联")
		}

		// 如果支付失败，目前简化为直接取消订单，重新支付时再创建新的订单
		_, err := h.orderService.CancelOrder(ctx, createdOrder.ID)
		if err != nil {
			log.Printf("Failed to cancel order: %v", err)
			// 订单取消失败时返回500错误
			return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
				models.CodeInternalServerError,
				"Payment failed and order cancellation failed",
			))
		}

		// 支付失败，返回400错误状态码，但包含已取消的订单信息
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodePaymentFailed,
			"Payment processing failed",
		))
	}

	// 支付处理成功
	log.Printf("订单 %s 支付处理完成，支付ID: %d，状态: %s\n",
		createdOrder.OrderNumber, paymentResp.PaymentID, paymentResp.Status)

	// 支付成功时，确认燃油交易关联
	log.Printf("支付成功，开始确认燃油交易关联 - 燃油交易ID: %d, 订单ID: %d", fuelTransactionID, createdOrder.ID)
	
	confirmErr := h.fuelTransactionService.ConfirmFuelTransactionLink(ctx, fuelTransactionID, createdOrder.ID)
	if confirmErr != nil {
		log.Printf("确认燃油交易关联失败: %v", confirmErr)
		// 注意：这里记录错误但不中断流程，因为支付已成功
	} else {
		log.Printf("成功确认燃油交易关联")
	}

	// 创建支付记录（使用最终金额）
	now := time.Now()
	paymentMethodIDStr := formatPaymentMethodID(req.PaymentMethod)
	log.Printf("💳 创建支付记录 - 订单: %s, 支付方式ID: %s, 支付类型: %s", 
		createdOrder.OrderNumber, paymentMethodIDStr, req.PaymentType)
	log.Printf("💳 支付金额记录 - 原始: %.2f, 优惠: %.2f, 最终: %.2f", 
		allocatedAmount, discountAmount, finalAmount)
	
	payment := repository.OrderPayment{
		OrderID:       createdOrder.ID,
		PaymentMethod: paymentMethodIDStr, // 存储具体的支付方式ID
		Amount:        finalAmount,         // 🔄 使用促销后的最终金额
		Status:        repository.PaymentStatusCompleted,
		CompletedAt:   &now,  // 设置支付完成时间
		Metadata: map[string]interface{}{
			"payment_id":        paymentResp.PaymentID,
			"payment_number":    paymentResp.PaymentNumber,
			"payment_type":      req.PaymentType, // 将支付类型存储在metadata中
			"original_amount":   allocatedAmount,
			"discount_amount":   discountAmount,
			"final_amount":      finalAmount,
			"promotion_applied": promotionCalculationSuccess,
		},
	}

	_, err = h.orderService.AddPayment(ctx, createdOrder.ID, payment)
	if err != nil {
		// 添加支付记录失败, 记录错误
		log.Printf("Failed to add payment record: %v", err)
	}

	// 支付成功，完成订单
	completedOrder, err := h.orderService.CompleteOrder(ctx, createdOrder.ID)
	if err != nil {
		// 完成订单失败, 记录错误
		log.Printf("Failed to complete order: %v", err)
	}

	// 当支付成功并确认关联后，燃油交易状态会在ConfirmFuelTransactionLink方法内部自动更新
	// 这里不需要额外调用状态更新方法，因为确认关联时已经处理了状态检查
	log.Printf("燃油交易关联确认完成，状态已自动检查更新 - ID: %d", fuelTransactionID)

	createdOrder = completedOrder

	// 8. 最终验证和审计日志
	h.logAmountAuditTrail(createdOrder.OrderNumber, "支付完成", map[string]float64{
		"交易原始金额": allocatedAmount,
		"促销优惠金额": discountAmount,
		"最终支付金额": finalAmount,
		"支付服务金额": paymentResp.Amount,
	})
	
	// 验证支付金额与最终金额一致性
	if math.Abs(paymentResp.Amount - finalAmount) > 0.01 {
		log.Printf("⚠️ 支付金额与订单最终金额不一致 - 支付: %.2f, 订单: %.2f", paymentResp.Amount, finalAmount)
	}
	
	// 获取最新的订单信息用于返回和验证
	finalOrder, err := h.orderService.GetOrder(ctx, createdOrder.ID)
	if err != nil {
		log.Printf("⚠️ 获取最终订单信息失败: %v", err)
		finalOrder = createdOrder // 使用原有订单信息作为后备
	}
	
	// 进行订单完整性验证
	if err := h.validateOrderIntegrity(ctx, finalOrder.ID, map[string]float64{
		"final_amount": finalAmount,
		"discount_amount": discountAmount,
	}); err != nil {
		log.Printf("⚠️ 订单完整性验证失败: %v", err)
	}
	
	log.Printf("🎉 订单创建完成 - 订单号: %s, 最终金额: %.2f, 促销优惠: %.2f", 
		finalOrder.OrderNumber, finalAmount, discountAmount)

	// 9. 返回创建的订单 - 支付成功时返回200状态码
	return c.JSON(http.StatusOK, finalOrder)
}

// GetOrder 处理获取单个订单的请求
// @Summary 获取订单
// @Description 根据ID获取订单详情
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} repository.Order
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{id} [get]
func (h *OrderHandler) GetOrder(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	order, err := h.orderService.GetOrder(ctx, repository.IDFromInt64(id))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch order",
		))
	}

	if uuid.UUID(order.ID) == uuid.Nil {
		return c.JSON(http.StatusNotFound, models.NewErrorResponse(
			models.CodeNotFound,
			"Order not found",
		))
	}

	return c.JSON(http.StatusOK, order)
}

// AddOrderItemRequest 定义添加订单项的请求结构
type AddOrderItemRequest struct {
	ProductID   int64                  `json:"product_id" validate:"required"`
	ProductName string                 `json:"product_name" validate:"required"`
	ProductType string                 `json:"product_type" validate:"required"`
	Quantity    float64                `json:"quantity" validate:"required,gt=0"`
	UnitPrice   float64                `json:"unit_price" validate:"required,gt=0"`
	FuelGrade   *string                `json:"fuel_grade,omitempty"`
	PumpID      *string                `json:"pump_id,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AddOrderItem 处理添加订单项的请求
// @Summary 添加订单项
// @Description 向订单添加新的订单项
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param item body AddOrderItemRequest true "订单项信息"
// @Success 201 {object} repository.OrderItem
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{id}/items [post]
func (h *OrderHandler) AddOrderItem(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	var req AddOrderItemRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid request parameters",
		))
	}

	// 条件验证 - 只有在验证器存在时才进行验证
	if c.Echo().Validator != nil {
		if err := c.Validate(&req); err != nil {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeValidationError,
				"Request parameter validation failed",
			))
		}
	}

	// 计算总价
	totalPrice := req.Quantity * req.UnitPrice

	item := repository.OrderItem{
		ProductID:   int64ToUUID(req.ProductID),
		ProductName: req.ProductName,
		ProductType: req.ProductType,
		Quantity:    req.Quantity,
		UnitPrice:   req.UnitPrice,
		TotalPrice:  totalPrice,
		Metadata:    req.Metadata,
	}

	if req.FuelGrade != nil {
		item.FuelGrade = req.FuelGrade
	}

	if req.PumpID != nil {
		item.PumpID = req.PumpID
	}

	result, err := h.orderService.AddOrderItem(ctx, repository.IDFromInt64(id), item)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Order not found",
			))
		}
		if strings.Contains(err.Error(), "status") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid order status for adding items",
			))
		}
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to add order item",
		))
	}

	return c.JSON(http.StatusCreated, result)
}

// RemoveOrderItem 处理移除订单项的请求
// @Summary 移除订单项
// @Description 从订单中移除订单项
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param item_id path int true "订单项ID"
// @Success 204 {string} string "No Content"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{id}/items/{item_id} [delete]
func (h *OrderHandler) RemoveOrderItem(c echo.Context) error {
	ctx := context.Background()
	orderIDStr := c.Param("id")
	itemIDStr := c.Param("item_id")

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid order ID format",
		))
	}

	itemID, err := strconv.ParseInt(itemIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid item ID format",
		))
	}

	err = h.orderService.RemoveOrderItem(ctx, int64ToUUID(orderID), int64ToUUID(itemID))
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Order or order item not found",
			))
		}
		if strings.Contains(err.Error(), "status") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid order status for removing items",
			))
		}
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to remove order item",
		))
	}

	return c.NoContent(http.StatusNoContent)
}

// ApplyPromotionRequest 定义应用促销的请求结构
type ApplyPromotionRequest struct {
	PromotionID        string                 `json:"promotion_id" validate:"required"` // 改为string类型以支持UUID
	PromotionName      string                 `json:"promotion_name" validate:"required"`
	PromotionType      string                 `json:"promotion_type" validate:"required"`
	DiscountAmount     float64                `json:"discount_amount" validate:"required,gte=0"`
	FreeItemID         *int64                 `json:"free_item_id,omitempty"`
	FreeItemName       *string                `json:"free_item_name,omitempty"`
	FreeItemQuantity   *float64               `json:"free_item_quantity,omitempty"`
	MinimumOrderAmount *float64               `json:"minimum_order_amount,omitempty"`
	Metadata           map[string]interface{} `json:"metadata,omitempty"`
}

// ApplyPromotion 处理应用促销的请求
// @Summary 应用促销
// @Description 向订单应用促销
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param promotion body ApplyPromotionRequest true "促销信息"
// @Success 201 {object} repository.OrderPromotion
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{id}/promotions [post]
func (h *OrderHandler) ApplyPromotion(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	var req ApplyPromotionRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid request parameters",
		))
	}

	// 条件验证 - 只有在验证器存在时才进行验证
	if c.Echo().Validator != nil {
		if err := c.Validate(&req); err != nil {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeValidationError,
				"Request parameter validation failed",
			))
		}
	}

	promotion := repository.OrderPromotion{
		PromotionID:    req.PromotionID, // 直接使用string，不再转换为repository.ID
		PromotionName:  req.PromotionName,
		PromotionType:  req.PromotionType,
		DiscountAmount: req.DiscountAmount,
		Metadata:       req.Metadata,
	}

	if req.FreeItemID != nil {
		freeItemID := int64ToUUID(*req.FreeItemID)
		promotion.FreeItemID = &freeItemID
	}

	if req.FreeItemName != nil {
		promotion.FreeItemName = req.FreeItemName
	}

	if req.FreeItemQuantity != nil {
		promotion.FreeItemQuantity = req.FreeItemQuantity
	}

	if req.MinimumOrderAmount != nil {
		promotion.MinimumOrderAmount = req.MinimumOrderAmount
	}

	result, err := h.orderService.ApplyPromotion(ctx, repository.IDFromInt64(id), promotion)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Order not found",
			))
		}
		if strings.Contains(err.Error(), "status") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid order status for applying promotion",
			))
		}
		if strings.Contains(err.Error(), "already applied") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeConflict,
				"Promotion already applied to order",
			))
		}
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to apply promotion",
		))
	}

	return c.JSON(http.StatusCreated, result)
}

// CompleteOrder 处理完成订单的请求
// @Summary 完成订单
// @Description 将订单标记为已完成状态
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} repository.Order
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{id}/complete [post]
func (h *OrderHandler) CompleteOrder(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	result, err := h.orderService.CompleteOrder(ctx, repository.IDFromInt64(id))
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Order not found",
			))
		}
		if strings.Contains(err.Error(), "status") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid order status for completion",
			))
		}
		if strings.Contains(err.Error(), "insufficient payment") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Insufficient payment amount",
			))
		}
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to complete order",
		))
	}

	return c.JSON(http.StatusOK, result)
}

// CancelOrder 处理取消订单的请求
// @Summary 取消订单
// @Description 将订单标记为已取消状态
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} repository.Order
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{id}/cancel [post]
func (h *OrderHandler) CancelOrder(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	result, err := h.orderService.CancelOrder(ctx, repository.IDFromInt64(id))
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Order not found",
			))
		}
		if strings.Contains(err.Error(), "status") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid order status for cancellation",
			))
		}
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to cancel order",
		))
	}

	return c.JSON(http.StatusOK, result)
}

// GetOrderPaymentStatus 处理查询订单支付状态的请求
// @Summary 查询订单支付状态
// @Description 根据订单ID查询该订单的支付状态
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{id}/payment-status [get]
func (h *OrderHandler) GetOrderPaymentStatus(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	// 1. 获取订单信息
	order, err := h.orderService.GetOrder(ctx, repository.IDFromInt64(id))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch order",
		))
	}

	if uuid.UUID(order.ID) == uuid.Nil {
		return c.JSON(http.StatusNotFound, models.NewErrorResponse(
			models.CodeNotFound,
			"Order not found",
		))
	}

	// 2. 获取订单的支付记录
	payments, err := h.orderService.ListPayments(ctx, repository.IDFromInt64(id))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch order payments",
		))
	}

	// 3. 构建支付状态响应
	paymentStatusData := map[string]interface{}{
		"order_id":      order.ID,
		"order_number":  order.OrderNumber,
		"order_status":  order.Status,
		"total_amount":  order.TotalAmount,
		"paid_amount":   order.PaidAmount,
		"payment_count": len(payments),
		"payments":      []map[string]interface{}{},
	}

	// 4. 如果有支付记录，查询每个支付的详细状态
	if len(payments) > 0 {
		paymentDetails := make([]map[string]interface{}, 0, len(payments))

		for _, payment := range payments {
			paymentDetail := map[string]interface{}{
				"order_payment_id":     payment.ID,
				"payment_method":       payment.PaymentMethod,
				"amount":               payment.Amount,
				"order_payment_status": payment.Status,
				// "created_at":           payment.CreatedAt,
				// "updated_at":           payment.UpdatedAt,
				"metadata": payment.Metadata,
			}

			// 如果支付服务可用且有支付编号，尝试查询最新的支付状态
			if h.paymentService != nil && payment.Metadata != nil {
				if paymentNumberInterface, exists := payment.Metadata["payment_number"]; exists {
					paymentNumber := paymentNumberInterface.(string)

					// 查询支付服务中的最新状态
					paymentStatus, err := h.paymentService.QueryPaymentStatus(ctx, paymentNumber)
					if err == nil {
						paymentDetail["payment_status"] = paymentStatus
					} else {
						// 查询失败时记录错误，但不影响整体响应
						log.Printf("Failed to query payment status from payment service: %v", err)
						paymentDetail["status_query_error"] = err.Error()
					}
				}
			}

			paymentDetails = append(paymentDetails, paymentDetail)
		}

		paymentStatusData["payments"] = paymentDetails
	}

	// 5. 计算整体支付状态
	overallStatus := "unpaid"
	if order.PaidAmount > 0 {
		if order.PaidAmount >= order.TotalAmount {
			overallStatus = "fully_paid"
		} else {
			overallStatus = "partially_paid"
		}
	}

	// 如果订单已完成，状态为已支付
	if order.Status == repository.OrderStatusCompleted {
		overallStatus = "completed"
	} else if order.Status == repository.OrderStatusCancelled {
		overallStatus = "cancelled"
	}

	paymentStatusData["overall_payment_status"] = overallStatus

	return c.JSON(http.StatusOK, paymentStatusData)
}

// PromotionCalculatorRequest 促销计算请求结构
type PromotionCalculatorRequest struct {
	OrderID      string                 `json:"orderId"`
	UserID       string                 `json:"userId"`
	OrderAmount  float64                `json:"orderAmount"`
	OrderTime    string                 `json:"orderTime"`
	VehicleType  string                 `json:"vehicleType"`
	Items        []PromotionItem        `json:"items"`
}

// PromotionItem 促销计算商品项
type PromotionItem struct {
	ItemID      string                 `json:"itemId"`
	Name        string                 `json:"name"`
	CategoryIDs []string               `json:"categoryIds"`
	Quantity    float64                `json:"quantity"`
	Price       float64                `json:"price"`
	Attributes  map[string]interface{} `json:"attributes"`
}

// PromotionCalculatorResponse 促销计算响应结构
type PromotionCalculatorResponse struct {
	Success             bool                   `json:"success"`
	Message             string                 `json:"message"`
	OrderID             string                 `json:"orderId"`
	VehicleType         string                 `json:"vehicleType"`
	OriginalAmount      float64                `json:"originalAmount"`
	DiscountedAmount    float64                `json:"discountedAmount"`
	DiscountAmount      float64                `json:"discountAmount"`
	AppliedPromotions   []AppliedPromotion     `json:"appliedPromotions"`
	Items              []PromotionResponseItem `json:"items"`
	CalculationTime    string                 `json:"calculationTime"`
	TotalItems         int                    `json:"totalItems"`
	TotalQuantity      float64                `json:"totalQuantity"`
}



// PromotionResponseItem 促销计算响应商品项
type PromotionResponseItem struct {
	ItemID         string   `json:"itemId"`
	Name           string   `json:"name"`
	OriginalPrice  float64  `json:"originalPrice"`
	DiscountedPrice float64 `json:"discountedPrice"`
	Quantity       float64  `json:"quantity"`
	CategoryIDs    []string `json:"categoryIds"`
	Attributes     map[string]interface{} `json:"attributes"`
}

// mapFuelTypeToGrade 将系统内部燃油类型映射为营销引擎的燃油等级
func mapFuelTypeToGrade(fuelType string) string {
	fuelTypeMapping := map[string]string{
		"bp 92":           "BP_92",
		"Ultimate":        "ULTIMATE",
		"Ultimate Diesel": "ULTIMATE_DIESEL",
		"Diesel":          "DIESEL",
	}
	
	if grade, exists := fuelTypeMapping[fuelType]; exists {
		return grade
	}
	
	// 如果找不到映射，使用原始值（转为大写并替换空格为下划线）
	grade := fuelType
	grade = fmt.Sprintf("%s", grade)
	// 可以添加更多的转换逻辑
	return grade
}

// buildPromotionCalculatorRequest 构建促销计算请求
func (h *OrderHandler) buildPromotionCalculatorRequest(
	orderNumber string,
	fuelTransaction *repository.FuelTransaction,
	allocatedAmount float64,
	req *CreateOrderRequest,
) *PromotionCalculatorRequest {
	
	// 确定用户ID，优先使用CustomerID，其次使用EmployeeNo
	var userID string
	if req.CustomerID != nil {
		userID = fmt.Sprintf("customer_%d", *req.CustomerID)
	} else if req.EmployeeNo != nil {
		userID = fmt.Sprintf("employee_%s", *req.EmployeeNo)
	} else {
		userID = fmt.Sprintf("station_%d", req.StationID)
	}
	
	// 从metadata中获取车辆类型，默认为CAR
	vehicleType := "CAR"
	if req.Metadata != nil {
		// 优先查找顶层的vehicle_type
		if vt, exists := req.Metadata["vehicle_type"]; exists {
			vehicleType = extractVehicleTypeFromValue(vt, "顶层")
		} else {
			// 如果顶层没有，查找member_info中的vehicle_type
			if memberInfo, exists := req.Metadata["member_info"]; exists {
				if memberInfoMap, ok := memberInfo.(map[string]interface{}); ok {
					if vt, exists := memberInfoMap["vehicle_type"]; exists {
						vehicleType = extractVehicleTypeFromValue(vt, "member_info")
					}
				}
			}

			// 如果还是没有，查找erp_info中的vehicleType（注意大小写）
			if vehicleType == "CAR" {
				if erpInfo, exists := req.Metadata["erp_info"]; exists {
					if erpInfoMap, ok := erpInfo.(map[string]interface{}); ok {
						if vt, exists := erpInfoMap["vehicleType"]; exists {
							vehicleType = extractVehicleTypeFromValue(vt, "erp_info.vehicleType")
						}
					}
				}
			}

			if vehicleType == "CAR" {
				log.Printf("🚗 [车辆类型] 在所有位置都未找到vehicle_type字段，使用默认值: %s", vehicleType)
			}
		}

		// 记录完整的metadata用于调试
		log.Printf("🔍 [调试] 完整metadata: %+v", req.Metadata)
	} else {
		log.Printf("🚗 [车辆类型] metadata为空，使用默认值: %s", vehicleType)
	}

	// 标准化车辆类型值
	vehicleType = normalizeVehicleType(vehicleType)
	log.Printf("🚗 [车辆类型] 标准化后的值: %s", vehicleType)

	// 直接使用燃油交易的FuelType字段作为category_ids
	fuelType := fuelTransaction.FuelType
	
	// 构建商品项
	item := PromotionItem{
		ItemID:      fuelType,
		Name:        "燃油",
		CategoryIDs: []string{fuelType, "fuel", vehicleType},
		Quantity:    fuelTransaction.Volume,
		Price:       fuelTransaction.UnitPrice,
		Attributes: map[string]interface{}{
			"fuel_volume":        fuelTransaction.Volume,
			"original_fuel_type": fuelTransaction.FuelType,
			"fuel_grade":         fuelTransaction.FuelGrade,
		},
	}
	
	return &PromotionCalculatorRequest{
		OrderID:     orderNumber,
		UserID:      userID,
		OrderAmount: allocatedAmount,
		OrderTime:   time.Now().Format("2006-01-02T15:04:05.000Z"),
		VehicleType: vehicleType,
		Items:       []PromotionItem{item},
	}
}

// callPromotionCalculator 调用促销计算API
func (h *OrderHandler) callPromotionCalculator(ctx context.Context, request *PromotionCalculatorRequest) (*PromotionCalculatorResponse, error) {
	// 构建API URL
	calculatorURL := "http://localhost:8080/api/v1/calculator/process" // 可以从配置中获取
	
	// 序列化请求
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化促销请求失败: %w", err)
	}
	
	log.Printf("🎯 调用促销计算API - URL: %s", calculatorURL)
	log.Printf("🎯 促销请求数据: %s", string(jsonData))
	
	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", calculatorURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	
	httpReq.Header.Set("Content-Type", "application/json")
	
	// 设置合理的超时时间
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	
	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("调用促销计算API失败: %w", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应数据失败: %w", err)
	}
	
	log.Printf("🎯 促销计算API响应状态: %d", resp.StatusCode)
	log.Printf("🎯 促销计算API响应数据: %s", string(body))
	
	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("促销计算API返回错误状态: %d, 响应: %s", resp.StatusCode, string(body))
	}
	
	// 解析响应
	var response PromotionCalculatorResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析促销响应失败: %w", err)
	}
	
	// 验证响应
	if !response.Success {
		return nil, fmt.Errorf("促销计算失败: %s", response.Message)
	}
	
	return &response, nil
}

// validatePromotionResponse 验证促销计算响应的金额逻辑
func (h *OrderHandler) validatePromotionResponse(request *PromotionCalculatorRequest, response *PromotionCalculatorResponse) error {
	// 验证原始金额是否一致
	if math.Abs(response.OriginalAmount - request.OrderAmount) > 0.01 {
		return fmt.Errorf("促销响应原始金额不匹配: 请求=%.2f, 响应=%.2f", 
			request.OrderAmount, response.OriginalAmount)
	}
	
	// 验证金额计算逻辑
	if math.Abs(response.OriginalAmount - (response.DiscountedAmount + response.DiscountAmount)) > 0.01 {
		return fmt.Errorf("促销金额计算错误: 原始=%.2f, 优惠后=%.2f, 优惠=%.2f", 
			response.OriginalAmount, response.DiscountedAmount, response.DiscountAmount)
	}
	
	// 验证最终金额必须大于0
	if response.DiscountedAmount <= 0 {
		return fmt.Errorf("促销后金额必须大于0: %.2f", response.DiscountedAmount)
	}
	
	// 验证优惠金额不能为负数
	if response.DiscountAmount < 0 {
		return fmt.Errorf("优惠金额不能为负数: %.2f", response.DiscountAmount)
	}
	
	return nil
}

// createFallbackPromotionResponse 创建兜底的促销响应（无促销优惠）
func (h *OrderHandler) createFallbackPromotionResponse(originalAmount float64) *PromotionCalculatorResponse {
	return &PromotionCalculatorResponse{
		Success:           true,
		Message:           "使用兜底策略，无促销优惠",
		OriginalAmount:    originalAmount,
		DiscountedAmount:  originalAmount,
		DiscountAmount:    0,
		AppliedPromotions: []AppliedPromotion{},
		Items:            []PromotionResponseItem{},
		CalculationTime:  time.Now().Format("2006-01-02T15:04:05Z07:00"),
	}
}

// logPromotionError 记录促销相关错误的详细日志
func (h *OrderHandler) logPromotionError(stage string, orderNumber string, err error, request *PromotionCalculatorRequest) {
	log.Printf("🚨 促销处理错误 [%s] - 订单: %s", stage, orderNumber)
	log.Printf("🚨 错误详情: %v", err)
	if request != nil {
		log.Printf("🚨 请求数据: OrderID=%s, UserID=%s, Amount=%.2f, VehicleType=%s, Items=%d",
			request.OrderID, request.UserID, request.OrderAmount, request.VehicleType, len(request.Items))
	}
}

// validateAmountConsistency 验证金额一致性
func (h *OrderHandler) validateAmountConsistency(allocatedAmount, finalAmount, discountAmount float64) error {
	// 验证金额逻辑一致性
	expectedFinal := allocatedAmount - discountAmount
	if math.Abs(finalAmount - expectedFinal) > 0.01 {
		return fmt.Errorf("金额逻辑不一致: 原始=%.2f, 优惠=%.2f, 最终=%.2f, 期望最终=%.2f", 
			allocatedAmount, discountAmount, finalAmount, expectedFinal)
	}
	
	// 验证所有金额都非负
	if allocatedAmount < 0 || finalAmount < 0 || discountAmount < 0 {
		return fmt.Errorf("金额不能为负数: 原始=%.2f, 最终=%.2f, 优惠=%.2f", 
			allocatedAmount, finalAmount, discountAmount)
	}
	
	// 验证最终金额大于0
	if finalAmount <= 0 {
		return fmt.Errorf("最终金额必须大于0: %.2f", finalAmount)
	}
	
	return nil
}

// logAmountAuditTrail 记录金额审计轨迹
func (h *OrderHandler) logAmountAuditTrail(orderNumber string, stage string, amounts map[string]float64) {
	log.Printf("💰 [审计追踪] 订单: %s, 阶段: %s", orderNumber, stage)
	for key, value := range amounts {
		log.Printf("💰 [审计追踪] %s: %.2f", key, value)
	}
}

// logPromotionAuditTrail 记录促销审计轨迹  
func (h *OrderHandler) logPromotionAuditTrail(orderNumber string, stage string, data map[string]interface{}) {
	log.Printf("🎁 [促销审计] 订单: %s, 阶段: %s", orderNumber, stage)
	for key, value := range data {
		log.Printf("🎁 [促销审计] %s: %v", key, value)
	}
}

// validateOrderIntegrity 验证订单完整性
func (h *OrderHandler) validateOrderIntegrity(ctx context.Context, orderID repository.ID, expectedAmounts map[string]float64) error {
	// 获取订单详情验证
	order, err := h.orderService.GetOrder(ctx, orderID)
	if err != nil {
		return fmt.Errorf("获取订单失败: %w", err)
	}
	
	// 验证订单金额
	if expectedFinal, exists := expectedAmounts["final_amount"]; exists {
		if math.Abs(order.FinalAmount - expectedFinal) > 0.01 {
			return fmt.Errorf("订单最终金额不匹配: 期望=%.2f, 实际=%.2f", expectedFinal, order.FinalAmount)
		}
	}
	
	if expectedDiscount, exists := expectedAmounts["discount_amount"]; exists {
		if math.Abs(order.DiscountAmount - expectedDiscount) > 0.01 {
			return fmt.Errorf("订单优惠金额不匹配: 期望=%.2f, 实际=%.2f", expectedDiscount, order.DiscountAmount)
		}
	}
	
	return nil
}

// getFuelTypeProductID 将 fuel_type 转换为对应的 product_id
func (h *OrderHandler) getFuelTypeProductID(fuelType string) repository.ID {
	// 根据实际业务需求定义燃油类型到产品ID的映射
	fuelTypeMapping := map[string]int64{
		"gasoline":        1001, // 汽油类型
		"diesel":          1002, // 柴油类型
		"BP92":            1003, // BP 92号汽油
		"BP95":            1004, // BP 95号汽油 
		"Ultimate":        1005, // Ultimate汽油
		"Ultimate_Diesel": 1006, // Ultimate柴油
		"汽油":            1001, // 中文汽油
		"柴油":            1002, // 中文柴油
	}

	if productID, exists := fuelTypeMapping[fuelType]; exists {
		return int64ToUUID(productID)
	}

	// 如果找不到映射，根据燃油类型的特征进行智能匹配
	fuelTypeLower := strings.ToLower(fuelType)
	if strings.Contains(fuelTypeLower, "diesel") || strings.Contains(fuelTypeLower, "柴油") {
		return int64ToUUID(1002) // 默认柴油ID
	}
	
	// 默认返回汽油类型ID
	return int64ToUUID(1001)
}

// extractVehicleTypeFromValue 从不同类型的值中提取车辆类型
func extractVehicleTypeFromValue(vt interface{}, source string) string {
	// 记录原始值用于调试
	log.Printf("🚗 [车辆类型] 从%s获取原始值: %v (类型: %T)", source, vt, vt)

	// 尝试多种类型转换
	switch v := vt.(type) {
	case string:
		if v != "" {
			log.Printf("🚗 [车辆类型] 从%s成功解析字符串: %s", source, v)
			return v
		}
	case float64:
		// 处理数字类型（可能来自JSON解析）
		var result string
		if v == 1 {
			result = "MOTORBIKE"
		} else if v == 2 {
			result = "CAR"
		} else if v == 3 {
			result = "MOTORBIKE" // 根据erp_info中vehicleType: 3的情况
		} else {
			result = "CAR"
		}
		log.Printf("🚗 [车辆类型] 从%s的数字转换: %.0f -> %s", source, v, result)
		return result
	case int:
		// 处理整数类型
		var result string
		if v == 1 {
			result = "MOTORBIKE"
		} else if v == 2 {
			result = "CAR"
		} else if v == 3 {
			result = "MOTORBIKE" // 根据erp_info中vehicleType: 3的情况
		} else {
			result = "CAR"
		}
		log.Printf("🚗 [车辆类型] 从%s的整数转换: %d -> %s", source, v, result)
		return result
	default:
		log.Printf("⚠️ [车辆类型] 从%s获取未知类型 %T，使用默认值: CAR", source, vt)
		return "CAR"
	}

	return "CAR"
}

// normalizeVehicleType 标准化车辆类型值
func normalizeVehicleType(vehicleType string) string {
	// 转换为大写进行比较
	upperType := strings.ToUpper(strings.TrimSpace(vehicleType))

	// 标准化映射
	switch upperType {
	case "MOTORBIKE", "MOTORCYCLE", "MOTOR", "2W", "2-WHEELER", "TWO_WHEELER":
		return "MOTORBIKE"
	case "CAR", "AUTOMOBILE", "4W", "4-WHEELER", "FOUR_WHEELER":
		return "CAR"
	case "TRUCK", "LORRY":
		return "TRUCK"
	case "BUS":
		return "BUS"
	default:
		// 如果是未知类型，记录日志并返回默认值
		if vehicleType != "" && upperType != "CAR" {
			log.Printf("⚠️ [车辆类型] 未知的车辆类型: %s，使用默认值: CAR", vehicleType)
		}
		return "CAR"
	}
}

// isTeraPayment 判断是否为tera支付方式
func (h *OrderHandler) isTeraPayment(paymentType string) bool {
	if paymentType == "" {
		return false
	}

	// 统一转换为小写进行比较
	lowerType := strings.ToLower(strings.TrimSpace(paymentType))

	// 支持的tera支付方式变体
	teraVariants := []string{
		"tera",
		"tera_payment",
		"tera_pay",
		"terapayment",
	}

	for _, variant := range teraVariants {
		if lowerType == variant {
			return true
		}
	}

	return false
}
