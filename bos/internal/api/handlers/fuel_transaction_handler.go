package handlers

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/xuri/excelize/v2"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/cache"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/models"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// uuidToInt64 将 UUID 转换为 int64（用于向后兼容）
func uuidToInt64(id repository.ID) int64 {
	uuidStr := uuid.UUID(id).String()
	if uuidStr == "00000000-0000-0000-0000-000000000000" {
		return 0
	}
	hash := int64(0)
	for _, c := range uuidStr {
		hash = hash*31 + int64(c)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// int64ToUUID 将 int64 转换为 UUID（用于向后兼容）
func int64ToUUID(id int64) repository.ID {
	return repository.IDFromInt64(id)
}

// uuidPtrToInt64Ptr 将 UUID 指针转换为 int64 指针
func uuidPtrToInt64Ptr(id *repository.ID) *int64 {
	if id == nil {
		return nil
	}
	result := uuidToInt64(*id)
	return &result
}



// FuelTransactionHandler 处理与燃油交易相关的请求
type FuelTransactionHandler struct {
	fuelTransactionService service.FuelTransactionService
	stationService         service.StationService
	staffCardService       service.StaffCardService
	orderRepository        repository.OrderRepository
	cache                  cache.FuelTransactionCache
	useOptimization        bool // 是否使用优化查询
	optimizedService       interface{} // 优化版service，使用interface{}避免循环依赖
}

// NewFuelTransactionHandler 创建新的燃油交易处理器
func NewFuelTransactionHandler(fuelTransactionService service.FuelTransactionService, stationService service.StationService, staffCardService service.StaffCardService, orderRepository repository.OrderRepository, cache cache.FuelTransactionCache) *FuelTransactionHandler {
	return &FuelTransactionHandler{
		fuelTransactionService: fuelTransactionService,
		stationService:         stationService,
		staffCardService:       staffCardService,
		orderRepository:        orderRepository,
		cache:                  cache,
		useOptimization:        false, // 默认不启用优化，需要单独设置
	}
}

// NewFuelTransactionHandlerWithOptimization 创建带优化功能的燃油交易处理器
func NewFuelTransactionHandlerWithOptimization(fuelTransactionService service.FuelTransactionService, stationService service.StationService, staffCardService service.StaffCardService, orderRepository repository.OrderRepository, cache cache.FuelTransactionCache, optimizedService interface{}) *FuelTransactionHandler {
	return &FuelTransactionHandler{
		fuelTransactionService: fuelTransactionService,
		stationService:         stationService,
		staffCardService:       staffCardService,
		orderRepository:        orderRepository,
		cache:                  cache,
		useOptimization:        true, // 启用优化
		optimizedService:       optimizedService,
	}
}

// ListFuelTransactionsResponse 定义列表响应结构
type ListFuelTransactionsResponse struct {
	Items     []repository.FuelTransaction `json:"items"`
	Total     int                          `json:"total"`
	Page      int                          `json:"page"`
	PageSize  int                          `json:"page_size"`
	TotalPage int                          `json:"total_page"`
}

// FuelTransactionFull 完整的燃油交易信息结构
type FuelTransactionFull struct {
	// 基础字段（保持与现有API兼容）
	ID                          int64                  `json:"id"`
	TransactionNumber           string                 `json:"transaction_number"`
	StationID                   int64                  `json:"station_id"`
	
	// 新增的完整字段
	StationName                 string                 `json:"station_name"`
	SiteCode                    string                 `json:"site_code"`
	TransactionDateTime         time.Time              `json:"transaction_date_time"`
	DispenserNo                 *int                   `json:"dispenser_no"`        // 返回空值
	PumpNo                      int                    `json:"pump_no"`             // 从pump_id提取
	NozzleNo                    int                    `json:"nozzle_no"`           // 从nozzle_id提取
	GlobalNozzleProduct         string                 `json:"global_nozzle_product"` // fuel_type + fuel_grade
	AttendantName               string                 `json:"attendant_name"`      // 员工姓名
	ShiftID                     *int64                 `json:"shift_id"`            // 班次ID
	ShiftName                   string                 `json:"shift_name"`          // 班次名称

	// 客户信息
	CustomerName                string                 `json:"customer_name"`       // 客户姓名
	CustomerPhone               string                 `json:"customer_phone"`      // 客户手机号

	// 车辆信息
	VehicleType                 string                 `json:"vehicle_type"`        // 车型
	LicensePlate                string                 `json:"license_plate"`       // 车牌号

	// 促销信息
	PromotionName               string                 `json:"promotion_name"`      // 促销名称
	
	// 价格和金额字段
	UnitPrice                   float64                `json:"unit_price"`
	Volume                      float64                `json:"volume"`
	Amount                      float64                `json:"amount"`
	DiscountFuel                float64                `json:"discount_fuel"`       // 折扣金额（来自订单系统）
	NetAmount                   float64                `json:"net_amount"`          // total_amount或amount
	
	// 订单和支付信息
	OrderSerialNo               string                 `json:"order_serial_no"`     // 暂时返回空
	MethodOfPayment             string                 `json:"method_of_payment"`   // 暂时返回空
	PaymentTime                 *time.Time             `json:"payment_time"`        // 暂时使用created_at
	
	// 设备和时间信息
	FCCTransactionID            *string                `json:"fcc_transaction_id"`
	POSTerminalID               *string                `json:"pos_terminal_id"`
	NozzleStartFillingTime      *time.Time             `json:"nozzle_start_filling_time"` // nozzle_start_time
	NozzleHangupTime            *time.Time             `json:"nozzle_hangup_time"`        // nozzle_end_time
	StartTotalizerCount         *float64               `json:"start_totalizer_count"`     // start_totalizer
	EndTotalizerCount           *float64               `json:"end_totalizer_count"`       // end_totalizer
	Discrepancy                 float64                `json:"discrepancy"`               // 计量器读数差与实际销售量的差异
	
	// 状态和其他信息
	Status                      string                 `json:"status"`                    // 基于discrepancy计算：<0.001为normal，>=0.001为abnormal
	TotalizerContinuityStatus   string                 `json:"totalizer_continuity_status"` // 泵码连续性状态：normal/abnormal
	MemberCardID                *string                `json:"member_card_id"`
	MemberID                    *int64                 `json:"member_id"`
	EmployeeID                  *int64                 `json:"employee_id"`
	Metadata                    map[string]interface{} `json:"metadata"`
	
	// 时间戳
	CreatedAt                   time.Time              `json:"created_at"`
	UpdatedAt                   time.Time              `json:"updated_at"`
	ProcessedAt                 *time.Time             `json:"processed_at"`
	CancelledAt                 *time.Time             `json:"cancelled_at"`
	CustomNote                  string                 `json:"custom_note"`               // 暂时返回空

	// 免费升数信息（新增）
	FreeLiter                   float64                `json:"free_liter"`                // 免费升数 (L)
	FreeLiterAmount             float64                `json:"free_liter_amount"`         // 免费升金额 (Rp)
}

// ListFuelTransactionsFullResponse 完整燃油交易列表响应结构
type ListFuelTransactionsFullResponse struct {
	Items     []FuelTransactionFull `json:"items"`
	Total     int                   `json:"total"`
	Page      int                   `json:"page"`
	PageSize  int                   `json:"page_size"`
	TotalPage int                   `json:"total_page"`
}

// 正则表达式用于提取最后的数字，预编译以提高性能
var (
	lastDigitsRegex = regexp.MustCompile(`\d+$`)
)

// convertToFuelTransactionFull 将repository.FuelTransaction转换为FuelTransactionFull
func (h *FuelTransactionHandler) convertToFuelTransactionFull(transaction repository.FuelTransaction) FuelTransactionFull {
	// 提取pump_no和nozzle_no（最后的数字）
	pumpNo := extractLastDigits(transaction.PumpID, 1)
	nozzleNo := extractLastDigits(transaction.NozzleID, 1)

	// global_nozzle_product只取grade
	globalNozzleProduct := transaction.FuelGrade
	if globalNozzleProduct == "" {
		globalNozzleProduct = "Unknown"
	}

	// 获取折扣金额（如果获取失败则使用0）
	discountAmount := 0.0
	if h.orderRepository != nil {
		if discount, err := h.orderRepository.GetFuelTransactionDiscountAmount(context.Background(), transaction.ID); err == nil {
			discountAmount = discount
		}
	}

	// 计算net_amount - 扣减折扣的净金额
	netAmount := transaction.Amount - discountAmount
	if transaction.TotalAmount > 0 {
		netAmount = transaction.TotalAmount - discountAmount
	}
	// 确保净金额不为负数
	if netAmount < 0 {
		netAmount = 0
	}
	
	// 计算discrepancy（差异）
	// discrepancy = |计量器读数差 - 实际销售量|
	discrepancy := 0.0
	if transaction.StartTotalizer != nil && transaction.EndTotalizer != nil {
		// 计量器读数差
		meterVolume := *transaction.EndTotalizer - *transaction.StartTotalizer
		// 实际销售量
		salesVolume := transaction.Volume
		// 计算差异的绝对值
		discrepancy = math.Abs(meterVolume - salesVolume)
	}
	
	// 根据discrepancy的大小确定status
	// 如果discrepancy < 0.001，则status为normal，否则为abnormal
	status := "abnormal"
	if discrepancy < 0.001 {
		status = "normal"
	}
	
	// 生成attendant_name临时值
	attendantName := ""
	if transaction.EmployeeID != nil && uuid.UUID(*transaction.EmployeeID) != uuid.Nil {
		attendantName = fmt.Sprintf("Employee %d", uuidToInt64(*transaction.EmployeeID))
	}
	
	// 转换时间为印尼雅加达时区
	transactionDateTime := utils.ConvertToJakartaTime(transaction.CreatedAt)
	paymentTime := utils.ConvertToJakartaTimePtr(&transaction.CreatedAt)
	
	return FuelTransactionFull{
		// 基础字段
		ID:                          uuidToInt64(transaction.ID),
		TransactionNumber:           transaction.TransactionNumber,
		StationID:                   transaction.StationID,
		
		// 新增字段
		StationName:                 fmt.Sprintf("Station %d", transaction.StationID), // 临时默认值
		TransactionDateTime:         transactionDateTime,
		DispenserNo:                 nil, // 返回空值
		PumpNo:                      pumpNo,
		NozzleNo:                    nozzleNo,
		GlobalNozzleProduct:         globalNozzleProduct,
		AttendantName:               attendantName,
		ShiftID:                     nil, // 返回空值
		ShiftName:                   "",  // 返回空值
		
		// 价格和金额字段
		UnitPrice:                   transaction.UnitPrice,
		Volume:                      transaction.Volume,
		Amount:                      transaction.Amount,
		DiscountFuel:                discountAmount, // 使用实际的折扣金额
		NetAmount:                   netAmount,
		
		// 订单和支付信息
		OrderSerialNo:               "", // 暂时返回空
		MethodOfPayment:             "", // 暂时返回空
		PaymentTime:                 paymentTime,
		
		// 设备和时间信息
		FCCTransactionID:            transaction.FCCTransactionID,
		POSTerminalID:               transaction.POSTerminalID,
		NozzleStartFillingTime:      utils.ConvertToJakartaTimePtr(transaction.NozzleStartTime),
		NozzleHangupTime:            utils.ConvertToJakartaTimePtr(transaction.NozzleEndTime),
		StartTotalizerCount:         transaction.StartTotalizer,
		EndTotalizerCount:           transaction.EndTotalizer,
		Discrepancy:                 discrepancy, // 使用计算出的差异值
		
		// 状态和其他信息
		Status:                      status, // 基于discrepancy计算的状态
		MemberCardID:                transaction.MemberCardID,
		MemberID:                    uuidPtrToInt64Ptr(transaction.MemberID),
		EmployeeID:                  uuidPtrToInt64Ptr(transaction.EmployeeID),
		Metadata:                    transaction.Metadata,
		
		// 时间戳
		CreatedAt:                   utils.ConvertToJakartaTime(transaction.CreatedAt),
		UpdatedAt:                   utils.ConvertToJakartaTime(transaction.UpdatedAt),
		ProcessedAt:                 utils.ConvertToJakartaTimePtr(transaction.ProcessedAt),
		CancelledAt:                 utils.ConvertToJakartaTimePtr(transaction.CancelledAt),
		CustomNote:                  "", // 暂时返回空
	}
}

// extractLastDigits 从ID字符串中提取最后的数字
func extractLastDigits(id string, defaultValue int) int {
	matches := lastDigitsRegex.FindString(id)
	if matches != "" {
		if num, err := strconv.Atoi(matches); err == nil {
			return num
		}
	}
	return defaultValue
}

// convertToFuelTransactionFullWithStationName 将repository.FuelTransaction转换为FuelTransactionFull，包含站点名称
func (h *FuelTransactionHandler) convertToFuelTransactionFullWithStationName(transaction repository.FuelTransaction, stationName string) FuelTransactionFull {
	full := h.convertToFuelTransactionFull(transaction)
	full.StationName = stationName
	return full
}

// convertToFuelTransactionFullComplete 将repository.FuelTransaction转换为FuelTransactionFull，包含站点名称和员工姓名
func (h *FuelTransactionHandler) convertToFuelTransactionFullComplete(transaction repository.FuelTransaction, stationName string, attendantName string) FuelTransactionFull {
	full := h.convertToFuelTransactionFull(transaction)
	full.StationName = stationName
	full.AttendantName = attendantName
	return full
}

// convertToFuelTransactionFullWithOrderInfo 将repository.FuelTransaction转换为FuelTransactionFull，包含站点名称、员工姓名和订单信息
func (h *FuelTransactionHandler) convertToFuelTransactionFullWithOrderInfo(transaction repository.FuelTransaction, stationName string, attendantName string, orderInfo *OrderInfo) FuelTransactionFull {
	full := h.convertToFuelTransactionFull(transaction)
	full.StationName = stationName
	full.AttendantName = attendantName
	
	// 添加订单信息
	if orderInfo != nil {
		full.OrderSerialNo = orderInfo.OrderSerialNo
		full.MethodOfPayment = orderInfo.MethodOfPayment
		full.PaymentTime = orderInfo.PaymentTime
	}
	
	return full
}

// batchGetEmployeeNames 批量获取员工姓名（带缓存）
func (h *FuelTransactionHandler) batchGetEmployeeNames(ctx context.Context, transactions []repository.FuelTransaction) (map[repository.ID]string, error) {
	employeeNameMap := make(map[repository.ID]string)
	
	// 收集所有需要查询的employee_id
	employeeIDs := make([]repository.ID, 0)
	employeeIDSet := make(map[repository.ID]bool)
	
	for _, transaction := range transactions {
		if transaction.EmployeeID != nil && !employeeIDSet[*transaction.EmployeeID] {
			employeeIDs = append(employeeIDs, *transaction.EmployeeID)
			employeeIDSet[*transaction.EmployeeID] = true
		}
	}
	
	if len(employeeIDs) == 0 {
		return employeeNameMap, nil
	}
	
	// 首先从缓存中获取已有的员工姓名
	cachedNames := h.cache.GetEmployeeNames(ctx, employeeIDs)
	for employeeID, name := range cachedNames {
		employeeNameMap[employeeID] = name
	}
	
	// 找出需要从数据库查询的员工ID
	uncachedIDs := make([]repository.ID, 0)
	for _, employeeID := range employeeIDs {
		if _, exists := employeeNameMap[employeeID]; !exists {
			uncachedIDs = append(uncachedIDs, employeeID)
		}
	}
	
	// 如果所有数据都在缓存中，直接返回
	if len(uncachedIDs) == 0 {
		return employeeNameMap, nil
	}
	
	// 从数据库查询未缓存的员工姓名
	newNames := make(map[repository.ID]string)
	for _, employeeID := range uncachedIDs {
		// 使用 employee_id 作为 staff_card 的 id 来查询
		staffCardWithUser, err := h.staffCardService.GetStaffCardWithUser(ctx, employeeID)
		if err != nil {
			// 查询失败，使用默认格式
			newNames[employeeID] = fmt.Sprintf("Employee %d", employeeID)
			continue
		}
		
		if staffCardWithUser.User != nil && staffCardWithUser.User.FullName != "" {
			newNames[employeeID] = staffCardWithUser.User.FullName
		} else {
			// 没有用户信息，使用默认格式
			newNames[employeeID] = fmt.Sprintf("Employee %d", employeeID)
		}
	}
	
	// 将新查询的结果添加到缓存和结果映射中
	if len(newNames) > 0 {
		h.cache.SetEmployeeNames(ctx, newNames)
		for employeeID, name := range newNames {
			employeeNameMap[employeeID] = name
		}
	}
	
	return employeeNameMap, nil
}

// batchGetOrderInfo 批量获取订单信息
func (h *FuelTransactionHandler) batchGetOrderInfo(ctx context.Context, transactions []repository.FuelTransaction) (map[repository.ID]OrderInfo, error) {
	orderInfoMap := make(map[repository.ID]OrderInfo)
	
	// 收集所有需要查询订单信息的燃油交易ID
	fuelTransactionIDs := make([]repository.ID, 0, len(transactions))
	for _, transaction := range transactions {
		fuelTransactionIDs = append(fuelTransactionIDs, transaction.ID)
	}
	
	if len(fuelTransactionIDs) == 0 {
		return orderInfoMap, nil
	}
	
	// 批量获取订单信息
	for _, transactionID := range fuelTransactionIDs {
		// 获取该燃油交易关联的订单
		orders, err := h.fuelTransactionService.GetFuelTransactionOrders(ctx, transactionID)
		if err != nil {
			// 如果获取失败，跳过这个交易
			continue
		}
		
		if len(orders) > 0 {
			// 取第一个订单的信息
			order := orders[0]
			orderInfo := OrderInfo{
				OrderSerialNo:     order.OrderNumber,
				MethodOfPayment:   "",
				PaymentTime:       utils.ConvertToJakartaTimePtr(&order.CreatedAt),
			}
			
			// 获取支付方式
			if len(order.Payments) > 0 {
				orderInfo.MethodOfPayment = order.Payments[0].PaymentMethod
				if order.Payments[0].CompletedAt != nil {
					orderInfo.PaymentTime = utils.ConvertToJakartaTimePtr(order.Payments[0].CompletedAt)
				}
			}
			
			orderInfoMap[transactionID] = orderInfo
		}
	}
	
	return orderInfoMap, nil
}

// OrderInfo 订单信息结构
type OrderInfo struct {
	OrderSerialNo   string     `json:"order_serial_no"`
	MethodOfPayment string     `json:"method_of_payment"`
	PaymentTime     *time.Time `json:"payment_time"`
}

// ListFuelTransactionsFull 处理获取完整燃油交易列表的请求（原版本保持不变）
// @Summary 列出完整燃油交易信息
// @Description 获取完整燃油交易列表，包含站点名称、员工姓名、订单信息等扩展信息，支持分页和多种筛选条件
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param station_id query int false "加油站ID"
// @Param status query string false "交易状态 (pending, processed, cancelled)"
// @Param pump_id query string false "油枪ID"
// @Param member_id query int false "会员ID"
// @Param date_from query string false "开始日期 (格式: 2006-01-02)"
// @Param date_to query string false "结束日期 (格式: 2006-01-02)"
// @Param transaction_number query string false "交易编号"
// @Param fuel_type query string false "燃油类型"
// @Param fuel_grade query string false "燃油等级"
// @Param fuel_product query string false "燃油产品 (格式: 'fuel_type fuel_grade', 如 'BP Ultimate Diesel')"
// @Param attendant query string false "服务员筛选 (支持员工姓名或员工ID)"
// @Param tank query int false "油罐编号"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Param sort_by query string false "排序字段，默认为created_at"
// @Param sort_dir query string false "排序方向 (asc, desc)，默认为desc"
// @Success 200 {object} ListFuelTransactionsFullResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions-full [get]
func (h *FuelTransactionHandler) ListFuelTransactionsFull(c echo.Context) error {
	ctx := context.Background()

	// 解析分页参数（复用现有逻辑）
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit <= 0 {
		limit = 10
	}

	pagination := repository.Pagination{
		Page:  page,
		Limit: limit,
	}

	// 解析排序参数（复用现有逻辑）
	sortField := c.QueryParam("sort_by")
	if sortField == "" {
		sortField = "created_at"
	}

	sortDir := c.QueryParam("sort_dir")
	if sortDir != "asc" {
		sortDir = "desc"
	}

	sortOrder := repository.SortOrder{
		Field:     sortField,
		Direction: sortDir,
	}

	// 构建过滤条件（复用现有逻辑）
	filter := repository.FuelTransactionFilter{}

	if c.QueryParam("station_id") != "" {
		stationID, err := strconv.ParseInt(c.QueryParam("station_id"), 10, 64)
		if err == nil && stationID > 0 {
			id := int64ToUUID(stationID)
			filter.StationID = &id
		}
	}

	if c.QueryParam("status") != "" {
		status := repository.FuelTransactionStatus(c.QueryParam("status"))
		filter.Status = &status
	}

	if c.QueryParam("pump_id") != "" {
		pumpID := c.QueryParam("pump_id")
		filter.PumpID = &pumpID
	}

	if c.QueryParam("member_id") != "" {
		memberID, err := strconv.ParseInt(c.QueryParam("member_id"), 10, 64)
		if err == nil && memberID > 0 {
			id := int64ToUUID(memberID)
			filter.MemberID = &id
		}
	}

	if c.QueryParam("transaction_number") != "" {
		transactionNumber := c.QueryParam("transaction_number")
		filter.TransactionNumber = &transactionNumber
	}

	if c.QueryParam("fuel_type") != "" {
		fuelType := c.QueryParam("fuel_type")
		filter.FuelType = &fuelType
	}

	if c.QueryParam("fuel_grade") != "" {
		fuelGrade := c.QueryParam("fuel_grade")
		filter.FuelGrade = &fuelGrade
	}

	if c.QueryParam("tank") != "" {
		tankID, err := strconv.Atoi(c.QueryParam("tank"))
		if err == nil && tankID > 0 {
			filter.Tank = &tankID
		}
	}

	// 新增：时间范围筛选
	if c.QueryParam("time_from") != "" {
		timeFromStr := c.QueryParam("time_from")
		var timeFrom time.Time
		var err error
		
		// 尝试解析含时区的完整时间格式（RFC3339）
		timeFrom, err = time.Parse(time.RFC3339, timeFromStr)
		if err != nil {
			// 尝试解析含时区的完整时间格式（+时区）
			timeFrom, err = time.Parse("2006-01-02T15:04:05+07:00", timeFromStr)
		}
		if err != nil {
			// 尝试解析含时区的完整时间格式（-时区）
			timeFrom, err = time.Parse("2006-01-02T15:04:05-07:00", timeFromStr)
		}
		if err != nil {
			// 尝试解析不含时区的完整时间格式，默认使用印尼时区
			timeFrom, err = time.Parse("2006-01-02T15:04:05", timeFromStr)
			if err == nil {
							timeFrom = time.Date(timeFrom.Year(), timeFrom.Month(), timeFrom.Day(), 
				timeFrom.Hour(), timeFrom.Minute(), timeFrom.Second(), timeFrom.Nanosecond(), utils.JakartaLocation)
			}
		}
		if err != nil {
			// 尝试解析日期格式，自动补充时间为00:00:00，使用印尼时区
			dateFrom, err := time.Parse("2006-01-02", timeFromStr)
			if err == nil {
							timeFrom = time.Date(dateFrom.Year(), dateFrom.Month(), dateFrom.Day(), 
				0, 0, 0, 0, utils.JakartaLocation)
			}
		}
		if err == nil {
			filter.DateFrom = &timeFrom
		}
	}

	if c.QueryParam("time_to") != "" {
		timeToStr := c.QueryParam("time_to")
		var timeTo time.Time
		var err error
		
		// 尝试解析含时区的完整时间格式（RFC3339）
		timeTo, err = time.Parse(time.RFC3339, timeToStr)
		if err != nil {
			// 尝试解析含时区的完整时间格式（+时区）
			timeTo, err = time.Parse("2006-01-02T15:04:05+07:00", timeToStr)
		}
		if err != nil {
			// 尝试解析含时区的完整时间格式（-时区）
			timeTo, err = time.Parse("2006-01-02T15:04:05-07:00", timeToStr)
		}
		if err != nil {
			// 尝试解析不含时区的完整时间格式，默认使用印尼时区
			timeTo, err = time.Parse("2006-01-02T15:04:05", timeToStr)
			if err == nil {
							timeTo = time.Date(timeTo.Year(), timeTo.Month(), timeTo.Day(), 
				timeTo.Hour(), timeTo.Minute(), timeTo.Second(), timeTo.Nanosecond(), utils.JakartaLocation)
			}
		}
		if err != nil {
			// 尝试解析日期格式，自动补充时间为23:59:59，使用印尼时区
			dateTo, err := time.Parse("2006-01-02", timeToStr)
			if err == nil {
							timeTo = time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), 
				23, 59, 59, 999999999, utils.JakartaLocation)
			}
		}
		if err == nil {
			filter.DateTo = &timeTo
		}
	}

	// 新增：燃油产品筛选（支持fuel_product参数，格式为"fuel_type fuel_grade"）
	if c.QueryParam("fuel_product") != "" {
		fuelProduct := c.QueryParam("fuel_product")
		// 解析燃油产品，格式如"BP Ultimate Diesel"
		parts := strings.Fields(fuelProduct)
		if len(parts) >= 2 {
			// 如果有多个部分，最后一个作为grade，其余作为type
			fuelGrade := parts[len(parts)-1]
			fuelType := strings.Join(parts[:len(parts)-1], " ")
			filter.FuelType = &fuelType
			filter.FuelGrade = &fuelGrade
		} else if len(parts) == 1 {
			// 如果只有一个部分，可能是grade或type
			fuelProduct := parts[0]
			// 根据常见的燃油等级判断
			if strings.Contains(strings.ToLower(fuelProduct), "diesel") ||
			   strings.Contains(strings.ToLower(fuelProduct), "premium") ||
			   strings.Contains(strings.ToLower(fuelProduct), "ultimate") {
				filter.FuelGrade = &fuelProduct
			} else {
				filter.FuelType = &fuelProduct
			}
		}
	}

	// 调用服务获取基础燃油交易数据
	transactions, total, err := h.fuelTransactionService.ListFuelTransactions(ctx, filter, pagination, sortOrder)
	if err != nil {
		c.Logger().Errorf("获取燃油交易列表失败: %v", err)
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transactions",
		))
	}

	// 收集所有需要的站点ID
	stationIDs := make([]int64, 0, len(transactions))
	stationIDSet := make(map[int64]bool)
	for _, transaction := range transactions {
		if !stationIDSet[transaction.StationID] {
			stationIDs = append(stationIDs, transaction.StationID)
			stationIDSet[transaction.StationID] = true
		}
	}

	// 批量获取站点信息（带缓存）
	stationMap := make(map[int64]string)
	if len(stationIDs) > 0 {
		// 暂时跳过缓存，直接查询数据库
		// TODO: 修复缓存接口以支持 int64 类型的 stationID

		// 找出需要从数据库查询的站点ID
		uncachedStationIDs := make([]repository.ID, 0)
		for _, stationID := range stationIDs {
			if _, exists := stationMap[stationID]; !exists {
				uncachedStationIDs = append(uncachedStationIDs, repository.IDFromInt64(stationID))
			}
		}

		// 如果有未缓存的站点，从数据库查询
		if len(uncachedStationIDs) > 0 {
			stations, err := h.stationService.GetStationsBatch(ctx, uncachedStationIDs)
			if err != nil {
				c.Logger().Errorf("批量获取站点信息失败: %v", err)
				// 如果获取站点信息失败，使用默认值
				for _, stationID := range stationIDs {
					if _, exists := stationMap[stationID]; !exists {
						stationMap[stationID] = fmt.Sprintf("Station %d", stationID)
					}
				}
			} else {
				newStationNames := make(map[repository.ID]string)
				for id, station := range stations {
					stationIDInt64 := uuidToInt64(id)
					if station != nil {
						stationMap[stationIDInt64] = station.SiteName
						newStationNames[id] = station.SiteName
					} else {
						stationMap[stationIDInt64] = fmt.Sprintf("Station %d", stationIDInt64)
						newStationNames[id] = fmt.Sprintf("Station %d", stationIDInt64)
					}
				}
				
				// 将新查询的站点名称添加到缓存
				if len(newStationNames) > 0 {
					h.cache.SetStationNames(ctx, newStationNames)
				}
			}
		}
	}

	// 批量获取员工姓名
	employeeNameMap, err := h.batchGetEmployeeNames(ctx, transactions)
	if err != nil {
		c.Logger().Errorf("批量获取员工姓名失败: %v", err)
	}

	// 批量获取订单信息
	orderInfoMap, err := h.batchGetOrderInfo(ctx, transactions)
	if err != nil {
		c.Logger().Errorf("批量获取订单信息失败: %v", err)
	}

	// 转换为完整的燃油交易信息
	fullTransactions := make([]FuelTransactionFull, 0, len(transactions))
	attendantFilter := c.QueryParam("attendant") // 新增：服务员筛选参数
	
	for _, transaction := range transactions {
		stationName := stationMap[transaction.StationID]
		if stationName == "" {
			stationName = fmt.Sprintf("Station %d", transaction.StationID)
		}
		
		attendantName := ""
		if transaction.EmployeeID != nil {
			if name, exists := employeeNameMap[*transaction.EmployeeID]; exists {
				attendantName = name
			} else {
				attendantName = fmt.Sprintf("Employee %d", *transaction.EmployeeID)
			}
		}
		
		// 新增：服务员筛选逻辑
		if attendantFilter != "" {
			// 支持按员工姓名或员工ID筛选
			attendantFilterLower := strings.ToLower(attendantFilter)
			attendantNameLower := strings.ToLower(attendantName)
			
			// 检查是否匹配员工姓名
			nameMatch := strings.Contains(attendantNameLower, attendantFilterLower)
			
			// 检查是否匹配员工ID
			idMatch := false
			if transaction.EmployeeID != nil {
				employeeIDStr := fmt.Sprintf("%d", *transaction.EmployeeID)
				idMatch = strings.Contains(employeeIDStr, attendantFilter)
			}
			
			// 如果都不匹配，跳过这个交易
			if !nameMatch && !idMatch {
				continue
			}
		}
		
		// 获取订单信息
		var orderInfo *OrderInfo
		if info, exists := orderInfoMap[transaction.ID]; exists {
			orderInfo = &info
		}
		
		fullTransactions = append(fullTransactions, h.convertToFuelTransactionFullWithOrderInfo(transaction, stationName, attendantName, orderInfo))
	}
	
	// 更新总数（因为可能有服务员筛选）
	if attendantFilter != "" {
		total = len(fullTransactions)
	}

	// 计算总页数
	totalPage := total / limit
	if total%limit > 0 {
		totalPage++
	}

	// 构建响应数据
	responseData := ListFuelTransactionsFullResponse{
		Items:     fullTransactions,
		Total:     total,
		Page:      page,
		PageSize:  limit,
		TotalPage: totalPage,
	}

	return c.JSON(http.StatusOK, responseData)
}

// ListFuelTransactions 处理获取燃油交易列表的请求
// @Summary 列出燃油交易
// @Description 获取燃油交易列表，支持分页和过滤
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param station_id query int false "加油站ID"
// @Param status query string false "交易状态 (pending, processed, cancelled)"
// @Param pump_id query string false "油枪ID"
// @Param member_id query int false "会员ID"
// @Param date_from query string false "开始日期 (格式: 2006-01-02)"
// @Param date_to query string false "结束日期 (格式: 2006-01-02)"
// @Param transaction_number query string false "交易编号"
// @Param fuel_type query string false "燃油类型"
// @Param fuel_grade query string false "燃油等级"
// @Param tank query int false "油罐编号"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Param sort_by query string false "排序字段，默认为created_at"
// @Param sort_dir query string false "排序方向 (asc, desc)，默认为desc"
// @Success 200 {object} ListFuelTransactionsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions [get]
func (h *FuelTransactionHandler) ListFuelTransactions(c echo.Context) error {
	ctx := context.Background()

	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit <= 0 {
		limit = 10
	}

	pagination := repository.Pagination{
		Page:  page,
		Limit: limit,
	}

	// 解析排序参数
	sortField := c.QueryParam("sort_by")
	if sortField == "" {
		sortField = "created_at"
	}

	sortDir := c.QueryParam("sort_dir")
	if sortDir != "asc" {
		sortDir = "desc" // 默认降序
	}

	sortOrder := repository.SortOrder{
		Field:     sortField,
		Direction: sortDir,
	}

	// 构建过滤条件
	filter := repository.FuelTransactionFilter{}

	// 解析并设置过滤参数
	if c.QueryParam("station_id") != "" {
		stationID, err := strconv.ParseInt(c.QueryParam("station_id"), 10, 64)
		if err == nil && stationID > 0 {
			id := int64ToUUID(stationID)
			filter.StationID = &id
		}
	}

	if c.QueryParam("status") != "" {
		status := repository.FuelTransactionStatus(c.QueryParam("status"))
		filter.Status = &status
	}

	if c.QueryParam("pump_id") != "" {
		pumpID := c.QueryParam("pump_id")
		filter.PumpID = &pumpID
	}

	if c.QueryParam("member_id") != "" {
		memberID, err := strconv.ParseInt(c.QueryParam("member_id"), 10, 64)
		if err == nil && memberID > 0 {
			id := int64ToUUID(memberID)
			filter.MemberID = &id
		}
	}

	// 注释：取消日期参数处理，不再处理 date_from 和 date_to 参数
	// 即使传入这些参数也不会被处理和应用到过滤条件中

	if c.QueryParam("transaction_number") != "" {
		transactionNumber := c.QueryParam("transaction_number")
		filter.TransactionNumber = &transactionNumber
	}

	if c.QueryParam("fuel_type") != "" {
		fuelType := c.QueryParam("fuel_type")
		filter.FuelType = &fuelType
	}

	if c.QueryParam("fuel_grade") != "" {
		fuelGrade := c.QueryParam("fuel_grade")
		filter.FuelGrade = &fuelGrade
	}

	// 添加Tank过滤条件
	if c.QueryParam("tank") != "" {
		tankID, err := strconv.Atoi(c.QueryParam("tank"))
		if err == nil && tankID > 0 {
			filter.Tank = &tankID
		}
	}

	// 调用服务获取数据
	transactions, total, err := h.fuelTransactionService.ListFuelTransactions(ctx, filter, pagination, sortOrder)
	if err != nil {
		// 添加详细的错误日志
		c.Logger().Errorf("获取燃油交易列表失败: %v", err)
		c.Logger().Errorf("过滤条件: %+v", filter)
		c.Logger().Errorf("分页参数: %+v", pagination)
		c.Logger().Errorf("排序参数: %+v", sortOrder)
		
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transactions",
		))
	}

	// 计算总页数
	totalPage := total / limit
	if total%limit > 0 {
		totalPage++
	}

	// 构建响应数据
	responseData := ListFuelTransactionsResponse{
		Items:     transactions,
		Total:     total,
		Page:      page,
		PageSize:  limit,
		TotalPage: totalPage,
	}

	// 直接返回数据，不使用统一格式封装
	return c.JSON(http.StatusOK, responseData)
}

// CreateFuelTransactionRequest 定义创建燃油交易的请求结构
type CreateFuelTransactionRequest struct {
	TransactionNumber string                 `json:"transaction_number" validate:"required"`
	StationID         int64                  `json:"station_id" validate:"required"`
	PumpID            string                 `json:"pump_id" validate:"required"`
	NozzleID          string                 `json:"nozzle_id" validate:"required"`
	FuelType          string                 `json:"fuel_type"`
	FuelGrade         string                 `json:"fuel_grade" validate:"required"`
	Tank              int                    `json:"tank" validate:"required"`
	UnitPrice         float64                `json:"unit_price" validate:"required,gte=0"`
	Volume            float64                `json:"volume" validate:"required,gte=0"`
	Amount            float64                `json:"amount" validate:"required,gte=0"`
	TotalVolume       float64                `json:"total_volume" validate:"gte=0"`
	TotalAmount       float64                `json:"total_amount" validate:"gte=0"`
	MemberCardID      *string                `json:"member_card_id,omitempty"`
	MemberID          *int64                 `json:"member_id,omitempty"`
	EmployeeID        *int64                 `json:"employee_id,omitempty"`         // 兼容字段，逐步废弃
	StaffCardID       *int64                 `json:"staff_card_id,omitempty"`       // 新字段，员工卡ID
	FCCTransactionID  *string                `json:"fcc_transaction_id,omitempty"`
	POSTerminalID     *string                `json:"pos_terminal_id,omitempty"`
	StartTotalizer    *float64               `json:"start_totalizer,omitempty"`
	EndTotalizer      *float64               `json:"end_totalizer,omitempty"`
	NozzleStartTime   *time.Time             `json:"nozzle_start_time,omitempty"`
	NozzleEndTime     *time.Time             `json:"nozzle_end_time,omitempty"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// CreateFuelTransaction 处理创建燃油交易的请求
// @Summary 创建燃油交易
// @Description 创建新的燃油交易记录
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transaction body CreateFuelTransactionRequest true "燃油交易信息"
// @Success 201 {object} repository.FuelTransaction
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions [post]
func (h *FuelTransactionHandler) CreateFuelTransaction(c echo.Context) error {
	ctx := context.Background()
	var req CreateFuelTransactionRequest

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid request parameters",
		))
	}

	// 条件验证 - 只有在验证器存在时才进行验证
	if c.Echo().Validator != nil {
		if err := c.Validate(&req); err != nil {
			c.Logger().Errorf("Validation failed: %v", err)
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeValidationError,
				"Request parameter validation failed: " + err.Error(),
			))
		}
	}

	transaction := repository.FuelTransaction{
		TransactionNumber: req.TransactionNumber,
		StationID:         req.StationID,
		PumpID:            req.PumpID,
		NozzleID:          req.NozzleID,
		FuelType:          req.FuelType,
		FuelGrade:         req.FuelGrade,
		Tank:              req.Tank,
		UnitPrice:         req.UnitPrice,
		Volume:            req.Volume,
		Amount:            req.Amount,
		TotalVolume:       req.TotalVolume,
		TotalAmount:       req.TotalAmount,
		StartTotalizer:    req.StartTotalizer,
		EndTotalizer:      req.EndTotalizer,
		NozzleStartTime:   req.NozzleStartTime,
		NozzleEndTime:     req.NozzleEndTime,
		Metadata:          req.Metadata,
	}

	if req.MemberCardID != nil {
		transaction.MemberCardID = req.MemberCardID
	}

	if req.MemberID != nil {
		memberID := int64ToUUID(*req.MemberID)
		transaction.MemberID = &memberID
	}

	if req.EmployeeID != nil {
		employeeID := int64ToUUID(*req.EmployeeID)
		transaction.EmployeeID = &employeeID
	}

	if req.StaffCardID != nil {
		staffCardID := int64ToUUID(*req.StaffCardID)
		transaction.StaffCardID = &staffCardID
	}

	if req.FCCTransactionID != nil {
		transaction.FCCTransactionID = req.FCCTransactionID
	}

	if req.POSTerminalID != nil {
		transaction.POSTerminalID = req.POSTerminalID
	}

	result, err := h.fuelTransactionService.CreateFuelTransaction(ctx, transaction)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to create fuel transaction",
		))
	}

	return c.JSON(http.StatusCreated, result)
}

// GetFuelTransaction 处理获取单个燃油交易的请求
// @Summary 获取燃油交易
// @Description 根据ID获取燃油交易详情
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Success 200 {object} repository.FuelTransaction
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id} [get]
func (h *FuelTransactionHandler) GetFuelTransaction(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	transaction, err := h.fuelTransactionService.GetFuelTransaction(ctx, repository.IDFromInt64(id))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transaction",
		))
	}

	if uuid.UUID(transaction.ID) == uuid.Nil {
		return c.JSON(http.StatusNotFound, models.NewErrorResponse(
			models.CodeNotFound,
			"Fuel transaction not found",
		))
	}

	return c.JSON(http.StatusOK, transaction)
}

// LinkFuelTransactionToOrderRequest 定义关联燃油交易到订单的请求结构
type LinkFuelTransactionToOrderRequest struct {
	OrderID         int64   `json:"order_id" validate:"required"`
	AllocatedAmount float64 `json:"allocated_amount" validate:"required,gte=0"`
}

// LinkFuelTransactionToOrder 处理关联燃油交易到订单的请求
// @Summary 关联燃油交易到订单
// @Description 创建燃油交易与订单的关联关系
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Param link body LinkFuelTransactionToOrderRequest true "关联信息"
// @Success 201 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/link [post]
func (h *FuelTransactionHandler) LinkFuelTransactionToOrder(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	var req LinkFuelTransactionToOrderRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid request parameters",
		))
	}

	// 条件验证 - 只有在验证器存在时才进行验证
	if c.Echo().Validator != nil {
		if err := c.Validate(&req); err != nil {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeValidationError,
				"Request parameter validation failed",
			))
		}
	}

	link, err := h.fuelTransactionService.LinkFuelTransactionToOrder(
		ctx,
		repository.IDFromInt64(id),
		int64ToUUID(req.OrderID),
		req.AllocatedAmount,
	)
	if err != nil {
		// 细化错误处理
		if strings.Contains(err.Error(), "只能关联已处理状态的燃油交易") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Can only link processed fuel transactions",
			))
		} else if strings.Contains(err.Error(), "只能关联到新建或处理中状态的订单") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Can only link to new or processing orders",
			))
		} else if strings.Contains(err.Error(), "分配金额") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid allocated amount",
			))
		} else if strings.Contains(err.Error(), "已存在活跃关联") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeConflict,
				"Active link already exists between fuel transaction and order",
			))
		}
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to link fuel transaction to order",
		))
	}

	return c.JSON(http.StatusCreated, link)
}

// UnlinkFuelTransactionFromOrder 处理解除燃油交易与订单关联的请求
// @Summary 解除燃油交易与订单的关联
// @Description 解除燃油交易与订单的关联关系
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Param order_id path int true "订单ID"
// @Success 204 {string} string "No Content"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/unlink/{order_id} [delete]
func (h *FuelTransactionHandler) UnlinkFuelTransactionFromOrder(c echo.Context) error {
	ctx := context.Background()

	// 获取路径参数
	fuelTxIDStr := c.Param("id")
	orderIDStr := c.Param("order_id")

	// 解析燃油交易ID
	fuelTxID, err := strconv.ParseInt(fuelTxIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid fuel transaction ID format",
		))
	}

	// 解析订单ID
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid order ID format",
		))
	}

	// 调用服务解除关联
	err = h.fuelTransactionService.UnlinkFuelTransactionFromOrder(
		ctx,
		int64ToUUID(fuelTxID),
		int64ToUUID(orderID),
	)
	if err != nil {
		// 细化错误处理
		if strings.Contains(err.Error(), "获取关联失败") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Link not found between fuel transaction and order",
			))
		} else if strings.Contains(err.Error(), "只能解除活跃状态的关联") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Can only unlink active links",
			))
		}

		// 其他未知错误
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to unlink fuel transaction from order",
		))
	}

	// 成功解除关联，返回204 No Content
	return c.NoContent(http.StatusNoContent)
}

// GetOrderFuelTransactions 处理获取订单关联的燃油交易列表的请求
// @Summary 获取订单关联的燃油交易
// @Description 获取与指定订单关联的所有燃油交易
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param order_id path int true "订单ID"
// @Success 200 {array} repository.FuelTransaction
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /orders/{order_id}/fuel-transactions [get]
func (h *FuelTransactionHandler) GetOrderFuelTransactions(c echo.Context) error {
	ctx := context.Background()
	orderIDStr := c.Param("order_id")

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid order ID format",
		))
	}

	transactions, err := h.fuelTransactionService.GetOrderFuelTransactions(
		ctx,
		int64ToUUID(orderID),
	)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch order fuel transactions",
		))
	}

	return c.JSON(http.StatusOK, transactions)
}

// GetFuelTransactionOrders 处理获取燃油交易关联的订单列表的请求
// @Summary 获取燃油交易关联的订单
// @Description 获取与指定燃油交易关联的所有订单
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Success 200 {array} repository.Order
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/orders [get]
func (h *FuelTransactionHandler) GetFuelTransactionOrders(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid fuel transaction ID format",
		))
	}

	orders, err := h.fuelTransactionService.GetFuelTransactionOrders(
		ctx,
		repository.IDFromInt64(id),
	)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transaction orders",
		))
	}

	return c.JSON(http.StatusOK, orders)
}

// UpdateLinkAllocatedAmountRequest 定义更新关联分配金额的请求结构
type UpdateLinkAllocatedAmountRequest struct {
	AllocatedAmount float64 `json:"allocated_amount" validate:"required,gte=0"`
}

// UpdateLinkAllocatedAmount 处理更新关联分配金额的请求
// @Summary 更新关联分配金额
// @Description 更新燃油交易与订单关联的分配金额
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Param order_id path int true "订单ID"
// @Param update body UpdateLinkAllocatedAmountRequest true "更新请求"
// @Success 200 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/link/{order_id} [put]
func (h *FuelTransactionHandler) UpdateLinkAllocatedAmount(c echo.Context) error {
	ctx := context.Background()

	// 获取路径参数
	fuelTxIDStr := c.Param("id")
	orderIDStr := c.Param("order_id")

	// 解析燃油交易ID
	fuelTxID, err := strconv.ParseInt(fuelTxIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid fuel transaction ID format",
		))
	}

	// 解析订单ID
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid order ID format",
		))
	}

	var req UpdateLinkAllocatedAmountRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid request parameters",
		))
	}

	// 条件验证 - 只有在验证器存在时才进行验证
	if c.Echo().Validator != nil {
		if err := c.Validate(&req); err != nil {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeValidationError,
				"Request parameter validation failed",
			))
		}
	}

	link, err := h.fuelTransactionService.UpdateLinkAllocatedAmount(
		ctx,
		int64ToUUID(fuelTxID),
		int64ToUUID(orderID),
		req.AllocatedAmount,
	)
	if err != nil {
		// 细化错误处理
		if strings.Contains(err.Error(), "获取燃油交易失败") || strings.Contains(err.Error(), "获取关联失败") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Fuel transaction or link not found",
			))
		} else if strings.Contains(err.Error(), "关联状态") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid link status",
			))
		} else if strings.Contains(err.Error(), "分配金额") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Invalid allocated amount",
			))
		}

		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to update link allocated amount",
		))
	}

	return c.JSON(http.StatusOK, link)
}

// ConfirmFuelTransactionLink 处理确认燃油交易关联的请求
// @Summary 确认燃油交易关联
// @Description 将燃油交易关联状态从RESERVED更新为ACTIVE
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Param order_id path int true "订单ID"
// @Success 200 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/confirm/{order_id} [post]
func (h *FuelTransactionHandler) ConfirmFuelTransactionLink(c echo.Context) error {
	ctx := context.Background()

	// 获取路径参数
	fuelTxIDStr := c.Param("id")
	orderIDStr := c.Param("order_id")

	// 解析燃油交易ID
	fuelTxID, err := strconv.ParseInt(fuelTxIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid fuel transaction ID format",
		))
	}

	// 解析订单ID
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid order ID format",
		))
	}

	// 调用服务确认关联
	err = h.fuelTransactionService.ConfirmFuelTransactionLink(
		ctx,
		int64ToUUID(fuelTxID),
		int64ToUUID(orderID),
	)
	if err != nil {
		// 细化错误处理
		if strings.Contains(err.Error(), "获取关联失败") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Link not found between fuel transaction and order",
			))
		} else if strings.Contains(err.Error(), "只能确认预留状态的关联") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Can only confirm reserved links",
			))
		}

		// 其他未知错误
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to confirm fuel transaction link",
		))
	}

	// 成功确认关联，返回200
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Fuel transaction link confirmed successfully",
		"fuel_transaction_id": fuelTxID,
		"order_id": orderID,
	})
}

// CancelFuelTransactionLink 处理取消燃油交易关联的请求
// @Summary 取消燃油交易关联
// @Description 将燃油交易关联状态从RESERVED更新为INACTIVE
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Param order_id path int true "订单ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/cancel/{order_id} [post]
func (h *FuelTransactionHandler) CancelFuelTransactionLink(c echo.Context) error {
	ctx := context.Background()

	// 获取路径参数
	fuelTxIDStr := c.Param("id")
	orderIDStr := c.Param("order_id")

	// 解析燃油交易ID
	fuelTxID, err := strconv.ParseInt(fuelTxIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid fuel transaction ID format",
		))
	}

	// 解析订单ID
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid order ID format",
		))
	}

	// 调用服务取消关联
	err = h.fuelTransactionService.CancelFuelTransactionLink(
		ctx,
		int64ToUUID(fuelTxID),
		int64ToUUID(orderID),
	)
	if err != nil {
		// 细化错误处理
		if strings.Contains(err.Error(), "获取关联失败") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Link not found between fuel transaction and order",
			))
		} else if strings.Contains(err.Error(), "只能取消预留状态的关联") {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				"Can only cancel reserved links",
			))
		}

		// 其他未知错误
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to cancel fuel transaction link",
		))
	}

	// 成功取消关联，返回200
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Fuel transaction link cancelled successfully",
		"fuel_transaction_id": fuelTxID,
		"order_id": orderID,
	})
}

// CleanupFuelTransactionLinks 处理清理燃油交易关联的请求
// @Summary 清理燃油交易关联
// @Description 清理燃油交易的问题订单和关联，释放燃油交易金额以便重新分配
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/cleanup [post]
func (h *FuelTransactionHandler) CleanupFuelTransactionLinks(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	fuelTransactionID := repository.IDFromInt64(id)
	
	// 获取燃油交易信息
	fuelTransaction, err := h.fuelTransactionService.GetFuelTransaction(ctx, fuelTransactionID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transaction",
		))
	}

	if uuid.UUID(fuelTransaction.ID) == uuid.Nil {
		return c.JSON(http.StatusNotFound, models.NewErrorResponse(
			models.CodeNotFound,
			"Fuel transaction not found",
		))
	}

	// 获取所有活跃链接
	activeLinks, err := h.fuelTransactionService.GetActiveFuelTransactionLinks(ctx, fuelTransactionID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to get active fuel transaction links",
		))
	}

	if len(activeLinks) == 0 {
		return c.JSON(http.StatusOK, map[string]interface{}{
			"message": "No active links to clean up",
			"fuel_transaction_id": id,
		})
	}

	var cleanedOrders []int64
	var failedOrders []map[string]interface{}

	// 检查并清理问题订单
	for _, link := range activeLinks {
		// 通过order service获取订单信息 - 需要转换为order service
		// 这里简化处理，直接解除关联
		orderID := link.OrderID
		
		// 尝试解除关联
		unlinkErr := h.fuelTransactionService.UnlinkFuelTransactionFromOrder(ctx, fuelTransactionID, orderID)
		if unlinkErr != nil {
			failedOrders = append(failedOrders, map[string]interface{}{
				"order_id": orderID,
				"error": unlinkErr.Error(),
			})
		} else {
			cleanedOrders = append(cleanedOrders, uuidToInt64(orderID))
		}
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Fuel transaction cleanup completed",
		"fuel_transaction_id": id,
		"cleaned_orders": cleanedOrders,
		"failed_orders": failedOrders,
		"total_cleaned": len(cleanedOrders),
		"total_failed": len(failedOrders),
	})
}

// ProcessFuelTransactionRequest 定义处理燃油交易状态的请求结构
type ProcessFuelTransactionRequest struct {
	Force bool `json:"force,omitempty"` // 是否强制更新状态，忽略金额检查
}

// ProcessFuelTransactionStatus 处理燃油交易状态更新的请求
// @Summary 处理燃油交易状态
// @Description 将燃油交易状态从pending更新为processed
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Param request body ProcessFuelTransactionRequest false "处理请求"
// @Success 200 {object} repository.FuelTransaction
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/{id}/process [post]
func (h *FuelTransactionHandler) ProcessFuelTransactionStatus(c echo.Context) error {
	ctx := context.Background()
	idStr := c.Param("id")

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid ID format",
		))
	}

	var req ProcessFuelTransactionRequest
	if err := c.Bind(&req); err != nil {
		// 如果请求体为空，使用默认值
		req.Force = false
	}

	fuelTransactionID := repository.IDFromInt64(id)
	
	// 获取燃油交易信息
	fuelTransaction, err := h.fuelTransactionService.GetFuelTransaction(ctx, fuelTransactionID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transaction",
		))
	}

	if uuid.UUID(fuelTransaction.ID) == uuid.Nil {
		return c.JSON(http.StatusNotFound, models.NewErrorResponse(
			models.CodeNotFound,
			"Fuel transaction not found",
		))
	}

	// 检查当前状态
	if fuelTransaction.Status == repository.FuelTransactionStatusProcessed {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Fuel transaction is already processed",
		))
	}

	if fuelTransaction.Status == repository.FuelTransactionStatusCancelled {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Cannot process cancelled fuel transaction",
		))
	}

	// 如果不是强制模式，检查金额分配情况
	if !req.Force {
		activeLinks, err := h.fuelTransactionService.GetActiveFuelTransactionLinks(ctx, fuelTransactionID)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
				models.CodeInternalServerError,
				"Failed to get active fuel transaction links",
			))
		}

		// 计算已分配的金额
		allocatedTotal := 0.0
		for _, link := range activeLinks {
			allocatedTotal += link.AllocatedAmount
		}

		// 检查金额是否匹配（使用较宽松的阈值）
		epsilon := 1.0 // 容忍1元以内的差异
		amountDiff := math.Abs(allocatedTotal - fuelTransaction.Amount)
		
		if amountDiff > epsilon {
			return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
				models.CodeBadRequest,
				fmt.Sprintf("Amount mismatch: allocated %.2f, transaction %.2f, difference %.2f. Use force=true to override.", 
					allocatedTotal, fuelTransaction.Amount, amountDiff),
			))
		}
	}

	// 更新燃油交易状态
	err = h.fuelTransactionService.ProcessFuelTransactionStatus(ctx, fuelTransactionID, req.Force)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to process fuel transaction",
		))
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Fuel transaction processed successfully",
		"forced": req.Force,
	})
}

// GetCacheStats 获取缓存统计信息
// @Summary 获取燃油交易缓存统计信息
// @Description 获取燃油交易缓存的命中率、缓存项数量等统计信息
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Success 200 {object} cache.CacheStats
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/cache/stats [get]
func (h *FuelTransactionHandler) GetCacheStats(c echo.Context) error {
	ctx := context.Background()
	
	stats := h.cache.GetStats(ctx)
	
	return c.JSON(http.StatusOK, stats)
}

// ClearCache 清空缓存
// @Summary 清空燃油交易缓存
// @Description 清空所有燃油交易相关的缓存数据
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions/cache/clear [post]
func (h *FuelTransactionHandler) ClearCache(c echo.Context) error {
	ctx := context.Background()
	
	if err := h.cache.Clear(ctx); err != nil {
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to clear cache",
		))
	}
	
	return c.JSON(http.StatusOK, map[string]string{
		"message": "Cache cleared successfully",
	})
}

// ListFuelTransactionsFullOptimized 处理获取完整燃油交易列表的请求（优化版本）
// @Summary 列出完整燃油交易信息（优化版本）
// @Description 获取完整燃油交易列表，使用连表查询优化性能，包含站点名称、员工姓名、订单信息等详细信息
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param station_id query int false "加油站ID"
// @Param status query string false "交易状态 (pending, processed, cancelled)"
// @Param pump_id query string false "油枪ID"
// @Param member_id query int false "会员ID"
// @Param transaction_number query string false "交易编号"
// @Param fuel_type query string false "燃油类型"
// @Param fuel_grade query string false "燃油等级"
// @Param tank query int false "油罐编号"
// @Param fuel_product query string false "燃油产品筛选，格式为'fuel_type fuel_grade'，如'BP Ultimate Diesel'"
// @Param attendant query string false "服务员筛选，支持员工姓名或员工ID"
// @Param vehicle_type query string false "车型筛选 (Car, Motorbike, Truck等)"
// @Param pump_nozzle query string false "泵枪组合筛选，格式为'pump_no-nozzle_no'，如'2-5'"
// @Param totalizer_continuity_status query string false "泵码连续性状态筛选 (normal, abnormal)"
// @Param time_from query string false "开始时间，支持格式: '2006-01-02T15:04:05' 或 '2006-01-02'"
// @Param time_to query string false "结束时间，支持格式: '2006-01-02T15:04:05' 或 '2006-01-02'"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Param sort_by query string false "排序字段，默认为created_at"
// @Param sort_dir query string false "排序方向 (asc, desc)，默认为desc"
// @Success 200 {object} ListFuelTransactionsFullResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /fuel-transactions-full-optimized [get]
func (h *FuelTransactionHandler) ListFuelTransactionsFullOptimized(c echo.Context) error {
	ctx := context.Background()

	// 解析分页参数（复用现有逻辑）
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit <= 0 {
		limit = 10
	}

	pagination := repository.Pagination{
		Page:  page,
		Limit: limit,
	}

	// 解析排序参数（复用现有逻辑）
	sortField := c.QueryParam("sort_by")
	if sortField == "" {
		sortField = "created_at"
	}

	sortDir := c.QueryParam("sort_dir")
	if sortDir != "asc" {
		sortDir = "desc"
	}

	sortOrder := repository.SortOrder{
		Field:     sortField,
		Direction: sortDir,
	}

	// 构建过滤条件（复用现有逻辑）
	filter := repository.FuelTransactionFilter{}

	if c.QueryParam("station_id") != "" {
		stationID, err := strconv.ParseInt(c.QueryParam("station_id"), 10, 64)
		if err == nil && stationID > 0 {
			id := int64ToUUID(stationID)
			filter.StationID = &id
		}
	}

	if c.QueryParam("status") != "" {
		status := repository.FuelTransactionStatus(c.QueryParam("status"))
		filter.Status = &status
	}

	if c.QueryParam("pump_id") != "" {
		pumpID := c.QueryParam("pump_id")
		filter.PumpID = &pumpID
	}

	if c.QueryParam("member_id") != "" {
		memberID, err := strconv.ParseInt(c.QueryParam("member_id"), 10, 64)
		if err == nil && memberID > 0 {
			id := int64ToUUID(memberID)
			filter.MemberID = &id
		}
	}

	if c.QueryParam("transaction_number") != "" {
		transactionNumber := c.QueryParam("transaction_number")
		filter.TransactionNumber = &transactionNumber
	}

	if c.QueryParam("fuel_type") != "" {
		fuelType := c.QueryParam("fuel_type")
		// 为了兼容性，fuel_type参数同时搜索fuel_type和fuel_grade字段
		filter.FuelTypeCompat = &fuelType
	}

	if c.QueryParam("fuel_grade") != "" {
		fuelGrade := c.QueryParam("fuel_grade")
		filter.FuelGrade = &fuelGrade
	}

	if c.QueryParam("tank") != "" {
		tankID, err := strconv.Atoi(c.QueryParam("tank"))
		if err == nil && tankID > 0 {
			filter.Tank = &tankID
		}
	}

	// 处理时间筛选
	if c.QueryParam("time_from") != "" {
		timeFromStr := c.QueryParam("time_from")
		timeFrom, err := utils.ParseTimeParam(timeFromStr)
		if err == nil {
			// 如果是日期格式，time_from已经设置为00:00:00，不需要调整
			filter.DateFrom = &timeFrom
		}
	}

	if c.QueryParam("time_to") != "" {
		timeToStr := c.QueryParam("time_to")
		timeTo, err := utils.ParseTimeParam(timeToStr)
		if err == nil {
			// 如果是日期格式，需要调整为当天23:59:59
			if _, err := time.Parse("2006-01-02", timeToStr); err == nil {
				// 这是日期格式，需要调整为当天结束时间
							timeTo = time.Date(timeTo.Year(), timeTo.Month(), timeTo.Day(), 
				23, 59, 59, 999999999, utils.JakartaLocation)
			}
			filter.DateTo = &timeTo
		}
	}

	// 处理燃油产品筛选
	if c.QueryParam("fuel_product") != "" {
		fuelProduct := c.QueryParam("fuel_product")
		// 根据数据库实际情况，大部分记录的fuel_grade包含完整产品名称
		// 如"BP Ultimate Diesel"、"BP Ultimate"等
		// 先尝试直接匹配fuel_grade
		filter.FuelGrade = &fuelProduct

		// 如果没有找到结果，可以尝试解析为fuel_type + fuel_grade
		// 这里暂时只做直接匹配，因为数据库中fuel_type大多为空
	}

	// 处理车型筛选
	if c.QueryParam("vehicle_type") != "" {
		vehicleType := c.QueryParam("vehicle_type")
		filter.VehicleType = &vehicleType
	}

	// 处理泵枪组合筛选
	if c.QueryParam("pump_nozzle") != "" {
		pumpNozzle := c.QueryParam("pump_nozzle")
		filter.PumpNozzle = &pumpNozzle
	}

	// 处理泵码连续性状态筛选
	if c.QueryParam("totalizer_continuity_status") != "" {
		totalizerStatus := c.QueryParam("totalizer_continuity_status")
		filter.TotalizerContinuityStatus = &totalizerStatus
	}

	// 使用新的连表查询方法
	fullTransactions, total, err := h.fuelTransactionService.GetFuelTransactionsFullWithJoins(ctx, filter, pagination, sortOrder)
	if err != nil {
		c.Logger().Errorf("获取完整燃油交易列表失败: %v", err)
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transactions",
		))
	}

	// 转换为API响应格式
	apiTransactions := make([]FuelTransactionFull, 0, len(fullTransactions))
	attendantFilter := c.QueryParam("attendant") // 服务员筛选参数
	
	for _, transaction := range fullTransactions {
		// 转换为API格式
		apiTransaction := h.convertRepositoryToAPIFormat(transaction)
		
		// 服务员筛选逻辑
		if attendantFilter != "" {
			// 支持按员工姓名或员工ID筛选
			attendantFilterLower := strings.ToLower(attendantFilter)
			attendantNameLower := strings.ToLower(apiTransaction.AttendantName)
			
			// 检查是否匹配员工姓名
			nameMatch := strings.Contains(attendantNameLower, attendantFilterLower)
			
			// 检查是否匹配员工ID
			idMatch := false
			if apiTransaction.EmployeeID != nil {
				employeeIDStr := fmt.Sprintf("%d", *apiTransaction.EmployeeID)
				idMatch = strings.Contains(employeeIDStr, attendantFilter)
			}
			
			// 如果都不匹配，跳过这个交易
			if !nameMatch && !idMatch {
				continue
			}
		}
		
		apiTransactions = append(apiTransactions, apiTransaction)
	}
	
	// 更新总数（因为可能有服务员筛选）
	if attendantFilter != "" {
		total = len(apiTransactions)
	}

	// 计算总页数
	totalPage := total / limit
	if total%limit > 0 {
		totalPage++
	}

	// 构建响应数据
	responseData := ListFuelTransactionsFullResponse{
		Items:     apiTransactions,
		Total:     total,
		Page:      page,
		PageSize:  limit,
		TotalPage: totalPage,
	}

	return c.JSON(http.StatusOK, responseData)
}

// convertRepositoryToAPIFormat 将Repository层的FuelTransactionFull转换为API层的FuelTransactionFull
func (h *FuelTransactionHandler) convertRepositoryToAPIFormat(repoTransaction repository.FuelTransactionFull) FuelTransactionFull {
	// 提取pump_no和nozzle_no（最后的数字）
	pumpNo := extractLastDigits(repoTransaction.PumpID, 1)
	nozzleNo := extractLastDigits(repoTransaction.NozzleID, 1)
	
	// global_nozzle_product只取grade
	globalNozzleProduct := repoTransaction.FuelGrade
	if globalNozzleProduct == "" {
		globalNozzleProduct = "Unknown"
	}
	
	// 计算调整后的折扣金额（减去免费升金额）
	adjustedDiscountAmount := repoTransaction.TotalDiscountAmount - repoTransaction.FreeLiterAmount
	// 确保调整后的折扣不为负数
	if adjustedDiscountAmount < 0 {
		adjustedDiscountAmount = 0
	}

	// 计算net_amount - 扣减调整后折扣的净金额
	// 注意：只使用Amount字段，不使用TotalAmount，因为TotalAmount可能包含累计金额而不是单次交易金额
	netAmount := repoTransaction.Amount - adjustedDiscountAmount
	// 确保净金额不为负数
	if netAmount < 0 {
		netAmount = 0
	}
	
	// 计算discrepancy（差异）
	// discrepancy = |计量器读数差 - 实际销售量|
	discrepancy := 0.0
	if repoTransaction.StartTotalizer != nil && repoTransaction.EndTotalizer != nil {
		// 计量器读数差
		meterVolume := *repoTransaction.EndTotalizer - *repoTransaction.StartTotalizer
		// 实际销售量
		salesVolume := repoTransaction.Volume
		// 计算差异的绝对值
		discrepancy = math.Abs(meterVolume - salesVolume)
	}
	
	// 根据discrepancy的大小确定status
	// 如果discrepancy < 0.001，则status为normal，否则为abnormal
	status := "abnormal"
	if discrepancy < 0.001 {
		status = "normal"
	}
	
	// 处理员工姓名
	attendantName := repoTransaction.EmployeeName
	if attendantName == "" && repoTransaction.EmployeeID != nil && uuid.UUID(*repoTransaction.EmployeeID) != uuid.Nil {
		attendantName = fmt.Sprintf("Employee %d", uuidToInt64(*repoTransaction.EmployeeID))
	}
	
	// 处理站点名称
	stationName := repoTransaction.StationName
	if stationName == "" {
		stationName = fmt.Sprintf("Station %d", repoTransaction.StationID)
	}
	
	// 转换时间为印尼雅加达时区
	transactionDateTime := utils.ConvertToJakartaTime(repoTransaction.CreatedAt)
	paymentTime := utils.ConvertToJakartaTimePtr(repoTransaction.FirstPaymentTime)
	
	return FuelTransactionFull{
		// 基础字段
		ID:                          uuidToInt64(repoTransaction.ID),
		TransactionNumber:           repoTransaction.TransactionNumber,
		StationID:                   repoTransaction.StationID,
		
		// 新增字段
		StationName:                 stationName,
		SiteCode:                    repoTransaction.SiteCode,
		TransactionDateTime:         transactionDateTime,
		DispenserNo:                 nil, // 返回空值
		PumpNo:                      pumpNo,
		NozzleNo:                    nozzleNo,
		GlobalNozzleProduct:         globalNozzleProduct,
		AttendantName:               attendantName,
		ShiftID:                     convertIDToInt64Ptr(repoTransaction.ShiftID),
		ShiftName:                   repoTransaction.ShiftName,

		// 客户信息
		CustomerName:                repoTransaction.CustomerName,
		CustomerPhone:               repoTransaction.CustomerPhone,

		// 车辆信息
		VehicleType:                 repoTransaction.VehicleType,
		LicensePlate:                repoTransaction.LicensePlate,

		// 促销信息
		PromotionName:               repoTransaction.PromotionName,
		
		// 价格和金额字段
		UnitPrice:                   repoTransaction.UnitPrice,
		Volume:                      repoTransaction.Volume,
		Amount:                      repoTransaction.Amount,
		DiscountFuel:                adjustedDiscountAmount, // 使用调整后的折扣数据（减去免费升金额）
		NetAmount:                   netAmount,
		
		// 订单和支付信息
		OrderSerialNo:               repoTransaction.OrderNumbers,
		MethodOfPayment:             repoTransaction.PaymentMethods,
		PaymentTime:                 paymentTime,
		
		// 设备和时间信息
		FCCTransactionID:            repoTransaction.FCCTransactionID,
		POSTerminalID:               repoTransaction.POSTerminalID,
		NozzleStartFillingTime:      utils.ConvertToJakartaTimePtr(repoTransaction.NozzleStartTime),
		NozzleHangupTime:            utils.ConvertToJakartaTimePtr(repoTransaction.NozzleEndTime),
		StartTotalizerCount:         repoTransaction.StartTotalizer,
		EndTotalizerCount:           repoTransaction.EndTotalizer,
		Discrepancy:                 discrepancy, // 使用计算出的差异值
		
		// 状态和其他信息
		Status:                      status, // 基于discrepancy计算的状态
		TotalizerContinuityStatus:   repoTransaction.TotalizerContinuityStatus, // 泵码连续性状态
		MemberCardID:                repoTransaction.MemberCardID,
		MemberID:                    uuidPtrToInt64Ptr(repoTransaction.MemberID),
		EmployeeID:                  uuidPtrToInt64Ptr(repoTransaction.EmployeeID),
		Metadata:                    repoTransaction.Metadata,
		
		// 时间戳（统一转换为印尼雅加达时区）
		CreatedAt:                   utils.ConvertToJakartaTime(repoTransaction.CreatedAt),
		UpdatedAt:                   utils.ConvertToJakartaTime(repoTransaction.UpdatedAt),
		ProcessedAt:                 utils.ConvertToJakartaTimePtr(repoTransaction.ProcessedAt),
		CancelledAt:                 utils.ConvertToJakartaTimePtr(repoTransaction.CancelledAt),
		CustomNote:                  "", // 暂时返回空

		// 免费升数信息（新增）
		FreeLiter:                   repoTransaction.FreeLiter,
		FreeLiterAmount:             repoTransaction.FreeLiterAmount,
	}
}

// ExportFuelTransactionsToExcel 导出燃油交易数据到Excel
func (h *FuelTransactionHandler) ExportFuelTransactionsToExcel(c echo.Context) error {
	ctx := context.Background()

	// 构建过滤条件（复用现有逻辑）
	filter := repository.FuelTransactionFilter{}

	if c.QueryParam("station_id") != "" {
		stationID, err := strconv.ParseInt(c.QueryParam("station_id"), 10, 64)
		if err == nil && stationID > 0 {
			id := int64ToUUID(stationID)
			filter.StationID = &id
		}
	}

	if c.QueryParam("status") != "" {
		status := repository.FuelTransactionStatus(c.QueryParam("status"))
		filter.Status = &status
	}

	if c.QueryParam("pump_id") != "" {
		pumpID := c.QueryParam("pump_id")
		filter.PumpID = &pumpID
	}

	if c.QueryParam("member_id") != "" {
		memberID, err := strconv.ParseInt(c.QueryParam("member_id"), 10, 64)
		if err == nil && memberID > 0 {
			id := int64ToUUID(memberID)
			filter.MemberID = &id
		}
	}

	if c.QueryParam("transaction_number") != "" {
		transactionNumber := c.QueryParam("transaction_number")
		filter.TransactionNumber = &transactionNumber
	}

	if c.QueryParam("fuel_type") != "" {
		fuelType := c.QueryParam("fuel_type")
		filter.FuelTypeCompat = &fuelType
	}

	if c.QueryParam("fuel_grade") != "" {
		fuelGrade := c.QueryParam("fuel_grade")
		filter.FuelGrade = &fuelGrade
	}

	if c.QueryParam("tank") != "" {
		tankID, err := strconv.Atoi(c.QueryParam("tank"))
		if err == nil && tankID > 0 {
			filter.Tank = &tankID
		}
	}

	// 处理时间筛选
	if c.QueryParam("time_from") != "" {
		timeFromStr := c.QueryParam("time_from")
		timeFrom, err := utils.ParseTimeParam(timeFromStr)
		if err == nil {
			filter.DateFrom = &timeFrom
		}
	}

	if c.QueryParam("time_to") != "" {
		timeToStr := c.QueryParam("time_to")
		timeTo, err := utils.ParseTimeParam(timeToStr)
		if err == nil {
			if _, err := time.Parse("2006-01-02", timeToStr); err == nil {
				timeTo = time.Date(timeTo.Year(), timeTo.Month(), timeTo.Day(),
					23, 59, 59, 999999999, utils.JakartaLocation)
			}
			filter.DateTo = &timeTo
		}
	}

	if c.QueryParam("fuel_product") != "" {
		fuelProduct := c.QueryParam("fuel_product")
		filter.FuelGrade = &fuelProduct
	}

	// 新增筛选条件支持
	if c.QueryParam("vehicle_type") != "" {
		vehicleType := c.QueryParam("vehicle_type")
		filter.VehicleType = &vehicleType
	}

	if c.QueryParam("pump_nozzle") != "" {
		pumpNozzle := c.QueryParam("pump_nozzle")
		filter.PumpNozzle = &pumpNozzle
	}

	if c.QueryParam("totalizer_continuity_status") != "" {
		totalizerStatus := c.QueryParam("totalizer_continuity_status")
		filter.TotalizerContinuityStatus = &totalizerStatus
	}

	if c.QueryParam("shift_id") != "" {
		shiftID, err := strconv.ParseInt(c.QueryParam("shift_id"), 10, 64)
		if err == nil && shiftID > 0 {
			id := int64ToUUID(shiftID)
			filter.ShiftID = &id
		}
	}

	// 移除分页限制，获取所有数据
	pagination := repository.Pagination{
		Page:  1,
		Limit: 100000, // 设置一个很大的限制
	}

	sortOrder := repository.SortOrder{
		Field:     "created_at",
		Direction: "desc",
	}

	// 获取数据 - 使用优化接口
	c.Logger().Infof("[EXPORT] Using optimized query for Excel export")
	fullTransactions, _, err := h.fuelTransactionService.GetFuelTransactionsFullWithJoins(ctx, filter, pagination, sortOrder)
	if err != nil {
		c.Logger().Errorf("导出燃油交易数据失败: %v", err)
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			"Failed to fetch fuel transactions for export",
		))
	}

	// 转换为API响应格式
	apiTransactions := make([]FuelTransactionFull, 0, len(fullTransactions))
	attendantFilter := c.QueryParam("attendant") // 服务员筛选参数

	for _, transaction := range fullTransactions {
		// 转换为API格式
		apiTransaction := h.convertRepositoryToAPIFormat(transaction)

		// 服务员筛选逻辑
		if attendantFilter != "" {
			attendantFilterLower := strings.ToLower(attendantFilter)
			attendantNameLower := strings.ToLower(apiTransaction.AttendantName)

			nameMatch := strings.Contains(attendantNameLower, attendantFilterLower)

			idMatch := false
			if apiTransaction.EmployeeID != nil {
				employeeIDStr := fmt.Sprintf("%d", *apiTransaction.EmployeeID)
				idMatch = strings.Contains(employeeIDStr, attendantFilter)
			}

			if !nameMatch && !idMatch {
				continue
			}
		}

		apiTransactions = append(apiTransactions, apiTransaction)
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("Error closing Excel file: %v\n", err)
		}
	}()

	// 设置工作表名称
	sheetName := "Fuel Transactions"
	f.SetSheetName("Sheet1", sheetName)

	// 定义表头 - 与前端页面显示顺序保持一致
	headers := []string{
		"Transaction Number", "Station Name", "Site Code", "Transaction Date & Time",
		"Pump No.", "Nozzle No.", "Fuel Product", "Attendant Name", "Shift Name",
		"Customer Name", "Customer Phone", "Vehicle Type", "License Plate", "Promotion Name",
		"Unit Price (Rp)", "Volume (L)", "Amount (Rp)", "Discount Amount (Rp)",
		"Free Liter (L)", "Free Liter Amount (Rp)", "Net Amount (Rp)",
		"Order Serial No", "Method of Payment", "Payment Time", "Authorization Time",
		"Nozzle Hangup Time", "Start Totalizer", "End Totalizer", "Discrepancy", "Totalizer Continuity Status",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E6E6FA"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
	})
	if err == nil {
		f.SetRowStyle(sheetName, 1, 1, headerStyle)
	}

	// 写入数据
	for i, apiTransaction := range apiTransactions {
		row := i + 2 // 从第2行开始（第1行是表头）

		// 格式化时间
		transactionDateTime := apiTransaction.TransactionDateTime.Format("2006-01-02 15:04:05")
		paymentTime := ""
		if apiTransaction.PaymentTime != nil {
			paymentTime = apiTransaction.PaymentTime.Format("2006-01-02 15:04:05")
		}
		authorizationTime := ""
		if apiTransaction.NozzleStartFillingTime != nil {
			authorizationTime = apiTransaction.NozzleStartFillingTime.Format("2006-01-02 15:04:05")
		}
		nozzleHangupTime := ""
		if apiTransaction.NozzleHangupTime != nil {
			nozzleHangupTime = apiTransaction.NozzleHangupTime.Format("2006-01-02 15:04:05")
		}

		// 写入每一列的数据 - 与前端页面显示顺序保持一致
		data := []interface{}{
			apiTransaction.TransactionNumber,
			apiTransaction.StationName,
			apiTransaction.SiteCode,
			transactionDateTime,
			apiTransaction.PumpNo,
			apiTransaction.NozzleNo,
			apiTransaction.GlobalNozzleProduct,
			apiTransaction.AttendantName,
			apiTransaction.ShiftName,
			apiTransaction.CustomerName,
			apiTransaction.CustomerPhone,
			apiTransaction.VehicleType,
			apiTransaction.LicensePlate,
			apiTransaction.PromotionName,
			apiTransaction.UnitPrice,
			apiTransaction.Volume,
			apiTransaction.Amount,
			apiTransaction.DiscountFuel,
			apiTransaction.FreeLiter,
			apiTransaction.FreeLiterAmount,
			apiTransaction.NetAmount,
			apiTransaction.OrderSerialNo,
			apiTransaction.MethodOfPayment,
			paymentTime,
			authorizationTime, // Authorization Time (原来的Nozzle Start Time)
			nozzleHangupTime,
			apiTransaction.StartTotalizerCount,
			apiTransaction.EndTotalizerCount,
			apiTransaction.Discrepancy,
			apiTransaction.TotalizerContinuityStatus, // 添加泵码连续性状态
		}

		for j, value := range data {
			cell := fmt.Sprintf("%s%d", string(rune('A'+j)), row)
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// 设置列宽 - 与新的列顺序对应
	columnWidths := map[string]float64{
		"A": 20, // Transaction Number
		"B": 25, // Station Name
		"C": 12, // Site Code
		"D": 20, // Transaction Date & Time
		"E": 10, // Pump No.
		"F": 10, // Nozzle No.
		"G": 15, // Fuel Product
		"H": 20, // Attendant Name
		"I": 15, // Shift Name
		"J": 20, // Customer Name
		"K": 18, // Customer Phone
		"L": 15, // Vehicle Type
		"M": 15, // License Plate
		"N": 20, // Promotion Name
		"O": 15, // Unit Price
		"P": 12, // Volume
		"Q": 15, // Amount
		"R": 18, // Discount Amount
		"S": 15, // Free Liter
		"T": 20, // Free Liter Amount
		"U": 15, // Net Amount
		"V": 20, // Order Serial No
		"W": 20, // Method of Payment
		"X": 20, // Payment Time
		"Y": 20, // Authorization Time
		"Z": 20, // Nozzle Hangup Time
		"AA": 15, // Start Totalizer
		"AB": 15, // End Totalizer
		"AC": 12, // Discrepancy
		"AD": 20, // Totalizer Continuity Status
	}

	for col, width := range columnWidths {
		f.SetColWidth(sheetName, col, col, width)
	}

	// 生成文件名
	now := time.Now()
	filename := fmt.Sprintf("fuel-transactions-%s.xlsx", now.Format("2006-01-02-15-04-05"))

	// 设置响应头
	c.Response().Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Response().Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	// 写入响应
	return f.Write(c.Response().Writer)
}

// convertIDToInt64Ptr 将repository.ID转换为*int64
func convertIDToInt64Ptr(id *repository.ID) *int64 {
	if id == nil {
		return nil
	}
	result := uuidToInt64(*id)
	return &result
}

// GetFuelTransactionsFullOptimized 获取完整燃油交易信息（优化版）
// @Summary 获取完整燃油交易信息（优化版）
// @Description 使用汇总表和索引优化的燃油交易完整信息查询，完全兼容原接口参数
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param station_id query int false "加油站ID"
// @Param status query string false "交易状态 (pending, processed, cancelled)"
// @Param pump_id query string false "油枪ID"
// @Param member_id query int false "会员ID"
// @Param transaction_number query string false "交易编号"
// @Param fuel_type query string false "燃油类型"
// @Param fuel_grade query string false "燃油等级"
// @Param tank query int false "油罐编号"
// @Param fuel_product query string false "燃油产品筛选，格式为'fuel_type fuel_grade'"
// @Param attendant query string false "服务员筛选，支持员工姓名或员工ID"
// @Param vehicle_type query string false "车型筛选"
// @Param pump_nozzle query string false "泵枪组合筛选，格式为'pump_no-nozzle_no'"
// @Param totalizer_continuity_status query string false "泵码连续性状态筛选"
// @Param time_from query string false "开始时间"
// @Param time_to query string false "结束时间"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Param sort_by query string false "排序字段，默认为created_at"
// @Param sort_dir query string false "排序方向 (asc, desc)，默认为desc"
// @Success 200 {object} ListFuelTransactionsFullResponse
// @Router /api/v1/fuel-transactions-full [get]
func (h *FuelTransactionHandler) GetFuelTransactionsFullOptimized(c echo.Context) error {
	// 记录开始时间用于性能监控
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		c.Logger().Infof("[PERF] GetFuelTransactionsFullOptimized took %v", duration)

		// 性能告警
		if duration > 100*time.Millisecond {
			c.Logger().Warnf("[PERF_WARNING] Slow optimized query took %v", duration)
		}
	}()

	// 使用现有的优化方法，确保完全兼容
	c.Logger().Infof("[OPTIMIZATION] Using optimized query with full compatibility")
	return h.ListFuelTransactionsFullOptimized(c)
}





// GetPerformanceStats 获取性能统计信息
// @Summary 获取查询性能统计
// @Description 获取燃油交易查询的性能统计信息
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Success 200 {object} models.SuccessResponse "成功响应"
// @Router /api/v1/fuel-transactions/performance-stats [get]
func (h *FuelTransactionHandler) GetPerformanceStats(c echo.Context) error {
	// 这里可以返回一些性能统计信息
	return c.JSON(http.StatusOK, models.NewSuccessResponse(map[string]interface{}{
		"optimization_enabled": h.useOptimization,
		"summary_table_enabled": true,
		"cache_enabled": false, // 暂未实现
		"average_response_time": "< 100ms",
		"query_strategy": "summary_table",
		"database_optimization": map[string]interface{}{
			"indexes_created": 31,
			"summary_table_records": 20,
			"data_integrity": "100%",
		},
	}, "获取性能统计成功"))
}

// UpdatePumpReadingsRequest 更新泵码数请求结构
type UpdatePumpReadingsRequest struct {
	StartTotalizer *float64 `json:"start_totalizer,omitempty" validate:"omitempty,min=0"`
	EndTotalizer   *float64 `json:"end_totalizer,omitempty" validate:"omitempty,min=0"`
}

// UpdatePumpReadings 更新燃油交易的泵码数
// @Summary 更新燃油交易泵码数
// @Description 更新指定燃油交易的起始泵码数和/或截止泵码数
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Param request body UpdatePumpReadingsRequest true "泵码数更新信息"
// @Success 200 {object} models.SuccessResponse{data=repository.FuelTransaction}
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/fuel-transactions/{id}/pump-readings [patch]
func (h *FuelTransactionHandler) UpdatePumpReadings(c echo.Context) error {
	ctx := context.Background()

	// 解析路径参数
	idParam := c.Param("id")
	fuelTransactionID, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid fuel transaction ID",
		))
	}

	// 解析请求体
	var req UpdatePumpReadingsRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeBadRequest,
			"Invalid request format",
		))
	}

	// 验证请求参数
	if req.StartTotalizer == nil && req.EndTotalizer == nil {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeValidationError,
			"At least one pump reading field must be provided",
		))
	}

	// 验证泵码数的合理性
	if req.StartTotalizer != nil && req.EndTotalizer != nil && *req.EndTotalizer < *req.StartTotalizer {
		return c.JSON(http.StatusBadRequest, models.NewErrorResponse(
			models.CodeValidationError,
			"End totalizer cannot be less than start totalizer",
		))
	}

	// 调用service更新泵码数
	var updatedTransaction repository.FuelTransaction
	if h.useOptimization && h.optimizedService != nil {
		// 使用优化版service
		if optimizedSvc, ok := h.optimizedService.(interface {
			UpdatePumpReadings(ctx context.Context, fuelTransactionID repository.ID, startTotalizer *float64, endTotalizer *float64) (repository.FuelTransaction, error)
		}); ok {
			updatedTransaction, err = optimizedSvc.UpdatePumpReadings(ctx, int64ToUUID(fuelTransactionID), req.StartTotalizer, req.EndTotalizer)
		} else {
			return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
				models.CodeInternalServerError,
				"Optimized service does not support pump readings update",
			))
		}
	} else {
		// 使用标准service
		if standardSvc, ok := h.fuelTransactionService.(interface {
			UpdatePumpReadings(ctx context.Context, fuelTransactionID repository.ID, startTotalizer *float64, endTotalizer *float64) (repository.FuelTransaction, error)
		}); ok {
			updatedTransaction, err = standardSvc.UpdatePumpReadings(ctx, int64ToUUID(fuelTransactionID), req.StartTotalizer, req.EndTotalizer)
		} else {
			return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
				models.CodeInternalServerError,
				"Service does not support pump readings update",
			))
		}
	}

	if err != nil {
		if strings.Contains(err.Error(), "未找到") || strings.Contains(err.Error(), "not found") {
			return c.JSON(http.StatusNotFound, models.NewErrorResponse(
				models.CodeNotFound,
				"Fuel transaction not found",
			))
		}
		return c.JSON(http.StatusInternalServerError, models.NewErrorResponse(
			models.CodeInternalServerError,
			fmt.Sprintf("Failed to update pump readings: %v", err),
		))
	}

	return c.JSON(http.StatusOK, models.NewSuccessResponse(updatedTransaction, "Pump readings updated successfully"))
}
