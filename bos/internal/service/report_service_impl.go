package service

import (
	"context"
	"fmt"
	"sort"
	"time"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/utils"
	orderRepository "gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	orderService "gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// ReportServiceImpl 报表服务实现
type ReportServiceImpl struct {
	fuelTransactionRepo orderRepository.FuelTransactionRepository
	stationService      orderService.StationService
}

// NewReportService 创建报表服务实例
func NewReportService(
	fuelTransactionRepo orderRepository.FuelTransactionRepository,
	stationService orderService.StationService,
) ReportService {
	return &ReportServiceImpl{
		fuelTransactionRepo: fuelTransactionRepo,
		stationService:      stationService,
	}
}

// GetNozzlePumpReport 获取油枪泵码报表
func (s *ReportServiceImpl) GetNozzlePumpReport(ctx context.Context, siteID int64, reportDate string, shiftID *int64) (*NozzlePumpReportData, error) {
	// 1. 验证参数
	if err := s.validateParams(siteID, reportDate); err != nil {
		return nil, err
	}

	// 2. 获取站点信息
	station, err := s.stationService.GetStationByID(ctx, orderRepository.IDFromInt64(siteID))
	if err != nil {
		return nil, fmt.Errorf("获取站点信息失败: %w", err)
	}

	// 3. 构建查询过滤器
	filter := s.buildTransactionFilter(siteID, reportDate, shiftID)

	// 4. 获取燃油交易数据
	transactions, _, err := s.fuelTransactionRepo.List(ctx, filter, orderRepository.Pagination{
		Page:  1,
		Limit: 10000, // 设置足够大的页面大小
	}, orderRepository.SortOrder{
		Field:     "created_at",
		Direction: "ASC",
	})
	if err != nil {
		return nil, fmt.Errorf("获取燃油交易数据失败: %w", err)
	}

	// 5. 处理数据并生成报表
	reportData := s.processTransactionData(transactions, *station, reportDate, shiftID)

	return reportData, nil
}

// validateParams 验证输入参数
func (s *ReportServiceImpl) validateParams(siteID int64, reportDate string) error {
	if siteID <= 0 {
		return fmt.Errorf("无效的站点ID: %d", siteID)
	}

	// 验证日期格式
	_, err := time.Parse("2006-01-02", reportDate)
	if err != nil {
		return fmt.Errorf("无效的日期格式: %s, 应为YYYY-MM-DD", reportDate)
	}

	return nil
}

// buildTransactionFilter 构建交易查询过滤器
func (s *ReportServiceImpl) buildTransactionFilter(siteID int64, reportDate string, shiftID *int64) orderRepository.FuelTransactionFilter {
	// 解析日期并设置时间范围（雅加达时区）
	date, _ := time.Parse("2006-01-02", reportDate)
	startTime := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, utils.JakartaLocation)
	endTime := time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 999999999, utils.JakartaLocation)

	stationIDRepo := orderRepository.IDFromInt64(siteID)
	statusCompleted := orderRepository.FuelTransactionStatusProcessed // 使用正确的状态常量

	filter := orderRepository.FuelTransactionFilter{
		StationID: &stationIDRepo,
		DateFrom:  &startTime,
		DateTo:    &endTime,
		Status:    &statusCompleted,
	}

	if shiftID != nil {
		shiftIDRepo := orderRepository.IDFromInt64(*shiftID)
		filter.ShiftID = &shiftIDRepo
	}

	return filter
}

// processTransactionData 处理交易数据并生成报表
func (s *ReportServiceImpl) processTransactionData(
	transactions []orderRepository.FuelTransaction,
	station orderRepository.Station,
	reportDate string,
	shiftID *int64,
) *NozzlePumpReportData {
	// 按油枪分组处理数据 - 修复：只按nozzle_id分组
	nozzleMap := make(map[string]*NozzlePumpReading)

	// 按时间排序交易记录
	sort.Slice(transactions, func(i, j int) bool {
		return transactions[i].CreatedAt.Before(transactions[j].CreatedAt)
	})

	for _, tx := range transactions {
		key := tx.NozzleID // 修复：只使用nozzle_id作为key

		if reading, exists := nozzleMap[key]; exists {
			// 更新现有记录
			s.updateNozzleReading(reading, tx)
		} else {
			// 创建新记录
			nozzleMap[key] = s.createNozzleReading(tx)
		}
	}

	// 转换为切片并计算差异
	var nozzleReadings []NozzlePumpReading
	for _, reading := range nozzleMap {
		s.calculateVariance(reading)
		nozzleReadings = append(nozzleReadings, *reading)
	}

	// 计算汇总数据
	summary := s.calculateSummary(nozzleReadings)

	// 构建报表头信息
	header := NozzlePumpReportHeader{
		SiteID:     uuidToInt64(station.ID),
		SiteName:   station.SiteName,
		ReportDate: reportDate,
		ReportTime: utils.GetNowInJakarta().Format(time.RFC3339),
	}

	if shiftID != nil {
		header.ShiftID = shiftID
		// TODO: 获取班次名称
		shiftName := fmt.Sprintf("Shift %d", *shiftID)
		header.ShiftName = &shiftName
	}

	return &NozzlePumpReportData{
		ReportHeader:   header,
		NozzleReadings: nozzleReadings,
		Summary:        summary,
	}
}

// createNozzleReading 创建新的油枪读数记录
func (s *ReportServiceImpl) createNozzleReading(tx orderRepository.FuelTransaction) *NozzlePumpReading {
	reading := &NozzlePumpReading{
		NozzleID:        s.formatDeviceID(tx.NozzleID),
		PumpID:          s.formatDeviceID(tx.PumpID),
		FuelType:        tx.FuelType,
		FuelGrade:       tx.FuelGrade,
		FuelName:        tx.FuelGrade, // 直接使用燃油等级，不添加前缀
		SalesVolume:     tx.Volume,
		SalesAmount:     tx.Amount,
		LastUpdated:     tx.UpdatedAt.Format(time.RFC3339),
	}

	// 修复：正确设置班次开始读数（第一个交易的StartTotalizer）
	if tx.StartTotalizer != nil {
		reading.OpeningReading = *tx.StartTotalizer
	}
	// 初始设置结束读数，后续会被updateNozzleReading更新为最后一个交易的值
	if tx.EndTotalizer != nil {
		reading.ClosingReading = *tx.EndTotalizer
	}

	return reading
}

// updateNozzleReading 更新油枪读数记录
func (s *ReportServiceImpl) updateNozzleReading(reading *NozzlePumpReading, tx orderRepository.FuelTransaction) {
	// 累加销售数据
	reading.SalesVolume += tx.Volume
	reading.SalesAmount += tx.Amount

	// 修复：正确更新班次结束读数（最后一个交易的EndTotalizer）
	if tx.EndTotalizer != nil {
		reading.ClosingReading = *tx.EndTotalizer
	}

	// 更新最后更新时间
	if tx.UpdatedAt.After(parseTime(reading.LastUpdated)) {
		reading.LastUpdated = tx.UpdatedAt.Format(time.RFC3339)
	}
}

// calculateVariance 计算差异
func (s *ReportServiceImpl) calculateVariance(reading *NozzlePumpReading) {
	reading.MeterDifference = reading.ClosingReading - reading.OpeningReading
	reading.Variance = reading.MeterDifference - reading.SalesVolume

	if reading.MeterDifference > 0 {
		reading.VariancePercentage = (reading.Variance / reading.MeterDifference) * 100
	}

	// 改进状态判断：增加容差范围
	const TOLERANCE = 0.5 // 0.5L容差

	if reading.Variance > TOLERANCE || reading.Variance < -TOLERANCE {
		reading.Status = "abnormal" // 超出容差范围，异常
	} else {
		reading.Status = "normal"   // 在容差范围内
	}
}

// calculateSummary 计算汇总数据
func (s *ReportServiceImpl) calculateSummary(readings []NozzlePumpReading) NozzlePumpReportSummary {
	summary := NozzlePumpReportSummary{
		TotalNozzles: len(readings),
	}

	for _, reading := range readings {
		summary.TotalVariance += reading.Variance
		summary.TotalSalesVolume += reading.SalesVolume
		summary.TotalSalesAmount += reading.SalesAmount

		if reading.Status == "normal" {
			summary.NormalCount++
		} else {
			summary.AbnormalCount++
		}
	}

	return summary
}

// parseTime 解析时间字符串
func parseTime(timeStr string) time.Time {
	t, _ := time.Parse(time.RFC3339, timeStr)
	return t
}

// formatDeviceID 格式化设备ID，取最后两位数，如果有特殊字符则取最后一位
func (s *ReportServiceImpl) formatDeviceID(deviceID string) string {
	if deviceID == "" {
		return ""
	}

	// 从后往前找数字
	runes := []rune(deviceID)
	var digits []rune

	for i := len(runes) - 1; i >= 0 && len(digits) < 2; i-- {
		if runes[i] >= '0' && runes[i] <= '9' {
			digits = append([]rune{runes[i]}, digits...)
		} else if len(digits) > 0 {
			// 遇到非数字字符且已经有数字，停止
			break
		}
	}

	if len(digits) == 0 {
		// 如果没有找到数字，返回原字符串的最后两个字符
		if len(runes) >= 2 {
			return string(runes[len(runes)-2:])
		}
		return deviceID
	}

	// 如果只有一位数字或者两位数字中有特殊字符，返回最后一位
	if len(digits) == 1 {
		return string(digits)
	}

	// 检查两位数字之间是否有特殊字符
	result := string(digits)
	if len(result) == 2 {
		// 在原字符串中查找这两个数字的位置
		lastDigitPos := -1
		secondLastDigitPos := -1

		for i := len(runes) - 1; i >= 0; i-- {
			if runes[i] == digits[1] && lastDigitPos == -1 {
				lastDigitPos = i
			} else if runes[i] == digits[0] && lastDigitPos != -1 && secondLastDigitPos == -1 {
				secondLastDigitPos = i
				break
			}
		}

		// 如果两个数字之间有非数字字符，只返回最后一位
		if secondLastDigitPos != -1 && lastDigitPos-secondLastDigitPos > 1 {
			return string(digits[1:])
		}
	}

	return result
}
